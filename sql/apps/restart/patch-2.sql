ALTER TABLE `restart_polizze`
  ADD `tipoGara` CHAR(2) NOT NULL  AFTER `codiceConvenzione`,
  ADD `dataEstrazione` DATE NOT NULL ,
  ADD `dataEmissione` DATE NOT NULL ,
  ADD `dataIncasso` DATE NOT NULL ,
  ADD `dataEffetto` DATE NOT NULL ,
  ADD `compagnia` CHAR(1) NOT NULL ,
  ADD `danniVita` CHAR(1) NOT NULL ,
  ADD `idIntermediario` varchar(5) NOT NULL ,
  ADD `delega` char(1) NOT NULL ,
  ADD `idSezione` char(2) NULL ,
  ADD `codiceBene` char(2) NOT NULL ,
  ADD `codiceCliente` char(2) NOT NULL ,
  ADD `motivoEntrata` char(1) NOT NULL ,
  ADD `famigliaProdotto` char(10) NOT NULL ,
  ADD `premioAnnuoImp` decimal(10,2) NOT NULL ,
  ADD `premioUnicoImp` decimal(10,2) NOT NULL ,
  ADD `versamentiAggiuntivi` decimal(10,2) NOT NULL ,
  ADD `deltaPremio` decimal(10,2) NOT NULL,
  ADD `codiceCampagna` varchar(8) NOT NULL,
  ADD `codicePartnership` varchar(8) NOT NULL,
  ADD `tipoCollettiva` varchar(8) NOT NULL,
  ADD `numeroMadre` varchar(8) NOT NULL,
  ADD `modulareVita` varchar(8) NOT NULL,
  ADD `otp` varchar(2) NOT NULL,
  ADD `fea` varchar(2) NOT NULL,
  ADD `trasmElettronicaDoc` varchar(2) NOT NULL,
  ADD `polizzaDigitale` varchar(2) NOT NULL;
