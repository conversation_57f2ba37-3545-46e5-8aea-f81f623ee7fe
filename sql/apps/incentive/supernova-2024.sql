
--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `inc_supernova_2024_2024_static`
--

drop table if exists inc_supernova_2024_static;
CREATE TABLE `inc_supernova_2024_static` (
  `agenzia_id` char(4) NOT NULL,
  `active` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_supernova_2024_2024_static`
--
ALTER TABLE `inc_supernova_2024_static`
    ADD PRIMARY KEY `agenzia_id` (`agenzia_id`);

--
-- <PERSON>iti per la tabella `inc_supernova_2024_2024_static`
--
ALTER TABLE `inc_supernova_2024_static`
    ADD CONSTRAINT `inc_supernova_2024_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `inc_dolcevita_data`
--

drop table if exists inc_supernova_2024_data;
CREATE TABLE `inc_supernova_2024_data` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `polizza` varchar(64) NOT NULL,
  `prodotto` varchar(6) NOT NULL,
  `premio` float NOT NULL,
  `nuovo` tinyint(1) NOT NULL,
  `otp` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_supernova_2024_data`
--
ALTER TABLE `inc_supernova_2024_data`
    ADD PRIMARY KEY (`id`),
    ADD KEY `agenzia_id` (`agenzia_id`);

--
-- AUTO_INCREMENT per la tabella `inc_supernova_2024_data`
--
ALTER TABLE `inc_supernova_2024_data`
    MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Limiti per la tabella `inc_supernova_2024_data`
--
ALTER TABLE `inc_supernova_2024_data`
    ADD CONSTRAINT `inc_supernova_2024_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);


drop table if exists inc_supernova_2024_status;
CREATE TABLE `inc_supernova_2024_status` (
    `agenzia_id` char(4) NOT NULL,
    `premioIPPT` float NOT NULL,
    `premioNC` float NOT NULL,
    `pezziOpen` smallint UNSIGNED NOT NULL,
    `incentIPPT` float NOT NULL,
    `incentNC` float NOT NULL,
    `incentOpen` float NOT NULL,
    `pezziOTP` smallint UNSIGNED NOT NULL,
    `incentOTP` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_supernova_2024_status`
--
ALTER TABLE `inc_supernova_2024_status`
    ADD PRIMARY KEY (`agenzia_id`);

--
-- Limiti per la tabella `inc_supernova_2024_status`
--
ALTER TABLE `inc_supernova_2024_status`
    ADD CONSTRAINT `inc_supernova_2024_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

drop view if exists vw_inc_supernova_2024_status;
create view vw_inc_supernova_2024_status as SELECT
   s.agenzia_id, a.localita, a.nome, a.area, ga.nome as areaName, a.district, gd.nome as districtName, s.incentIPPT, s.incentNC, s.incentOpen, s.incentOTP, s.premioIPPT, s.premioNC, s.pezziOpen, s.pezziOTP,
   (s.incentIPPT + s.incentNC + s.incentOpen + s.incentOTP) as incentTot,
   IF(s.incentIPPT + s.incentNC >= 650, 1, 0) as objReached
FROM `inc_supernova_2024_status` s
        join agenzie a on a.id = s.agenzia_id
        join geo_aree ga on a.area = ga.id
        join geo_districts gd on a.district = gd.id;