-- STEP 1: import sql

ALTER TABLE `cms_agenzie` CHANGE `publishable` `approved` TINYINT(1) NOT NULL DEFAULT '0' AFTER `agenzia_id`;
ALTER TABLE `cms_agenzie` DROP PRIMARY KEY, ADD UNIQUE (`agenzia_id`, `approved`) USING BTREE;
RENAME TABLE `cms_agenzie` TO `cms_agenzie_data`;

DROP VIEW IF EXISTS `vw_cms_agenzie_backend`;

DROP TABLE IF EXISTS `cms_agenzie_status`;
CREATE TABLE `cms_agenzie_status` (
    `agenzia_id` char(4) NOT NULL,
    `approved` tinyint(1) NOT NULL DEFAULT '0',
    `user_id` mediumint UNSIGNED NULL,
    `updated_at` timestamp DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`agenzia_id`) USING BTREE,
    CONSTRAINT `cms_agenzie_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
    CONSTRAINT `cms_agenzie_status_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- STEP 2: lanciare batch cmsAgenzie importAgenciesStatus
/* IMPORTANTE: lanciare dopo aver generato i dati di cms_agenzie_status */

-- STEP 3: eseguire sql fino a fine step 3

ALTER TABLE `cms_agenzie_data` DROP FOREIGN KEY `cms_agenzie_data_ibfk_1`;
ALTER TABLE `cms_agenzie_data` ADD CONSTRAINT `cms_agenzie_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `cms_agenzie_status`(`agenzia_id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

DROP VIEW IF EXISTS `vw_cms_agenzie_status`;
CREATE VIEW `vw_cms_agenzie_status` as
SELECT cs.agenzia_id, ag.nome, cs.approved, gd.nome as distretto, ga.nome as area
FROM cms_agenzie_status cs
    JOIN agenzie ag ON ag.id = cs.agenzia_id
    LEFT JOIN geo_aree ga ON ag.area = ga.id
    LEFT JOIN geo_districts gd ON ag.district = gd.id;

DROP VIEW IF EXISTS `vw_cms_agenzie_data`;
CREATE VIEW `vw_cms_agenzie_data` as
SELECT ca.agenzia_id, ag.nome, ca.approved, ca.agencyEntrancePhoto, ca.whatsapp, ca.description, ca.url, ca.pec, ca.text, ca.showWeeklySchedule, gd.nome as distretto, ga.nome as area
FROM cms_agenzie_status cs
         JOIN cms_agenzie_data ca ON cs.agenzia_id = ca.agenzia_id
         JOIN agenzie ag ON ag.id = ca.agenzia_id
         LEFT JOIN geo_aree ga ON ag.area = ga.id
         LEFT JOIN geo_districts gd ON ag.district = gd.id;

-- /STEP3

-- STEP 4: eseguire sql

ALTER TABLE `cms_agenzie_impiegati` ADD `approved` TINYINT(1) NOT NULL AFTER `agenzia_id`;

ALTER TABLE `cms_agenzie_impiegati` DROP FOREIGN KEY `cms_agenzie_impiegati_ibfk_1`;
ALTER TABLE `cms_agenzie_impiegati` DROP FOREIGN KEY `cms_agenzie_impiegati_ibfk_2`;
ALTER TABLE `cms_agenzie_impiegati` DROP PRIMARY KEY, ADD UNIQUE(`user_id`, `approved`);

DROP VIEW IF EXISTS `vw_cms_agenzie_impiegati`;
CREATE VIEW `vw_cms_agenzie_impiegati` as
SELECT i.user_id, u.type, u.nome, u.cognome, u.agenzia_id, i.approved, u.rui, u.email, u.ruolo, i.photo, i.position, i.showOnFrontend, i.privacy, i.description
FROM cms_agenzie_impiegati i
         JOIN users u ON u.id = i.user_id WHERE u.active = 1;

-- STEP 5: eseguire batch cmsAgenzie employeesVersioningInit