ALTER TABLE `cms_agenzie_status` ADD `standard` TINYINT(1) NOT NULL AFTER `approved`;

DROP VIEW IF EXISTS `vw_cms_agenzie_status`;
CREATE VIEW `vw_cms_agenzie_status` as
SELECT cs.agenzia_id, ag.nome, cs.approved, cs.standard, gd.nome as distretto, ga.nome as area
FROM cms_agenzie_status cs
         JOIN agenzie ag ON ag.id = cs.agenzia_id
         LEFT JOIN geo_aree ga ON ag.area = ga.id
         LEFT JOIN geo_districts gd ON ag.district = gd.id;