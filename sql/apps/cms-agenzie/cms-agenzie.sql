DROP TABLE IF EXISTS `cms_agenzie_orari`;
DROP TABLE IF EXISTS `cms_agenzie_impiegati`;
DROP TABLE IF EXISTS `cms_agenzie`;

CREATE TABLE `cms_agenzie` (
  `agenzia_id` char(4) NOT NULL,
  `agencyEntrancePhoto` VARCHAR(500) NULL,
  `pec` VARCHAR(50) NULL,
  `publishable` tinyint(1) NOT NULL DEFAULT '0',
  `url` VARCHAR(500) DEFAULT '',
  `description` TEXT DEFAULT '',
  `text` TINYTEXT DEFAULT '',
  `showWeeklySchedule` tinyint(1) NOT NULL DEFAULT '0',
  `whatsapp` varchar(10) NULL,
  PRIMARY KEY (`agenzia_id`) USING BTREE,
  CONSTRAINT `cms_agenzie_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `cms_agenzie_impiegati` (
  `user_id` MEDIUMINT(8) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `photo` VARCHAR(500) NULL,
  `position` ENUM('AUT', 'PER', 'BEN', 'RIS', 'WEL', 'PMI', 'CAU', 'AGR', 'SIN', 'AMM', 'FRO', 'BAC', 'CLI'),
  `description` TINYTEXT DEFAULT '',
  `showOnFrontend` tinyint(1) NOT NULL DEFAULT '0',
  `privacy` ENUM('YES','WAIT','NO','UNSET'),
  `privacyUpdatedAt` timestamp NULL DEFAULT null,
  PRIMARY KEY (`user_id`) USING BTREE,
  CONSTRAINT `cms_agenzie_impiegati_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
    CONSTRAINT `cms_agenzie_impiegati_ibfk_2` FOREIGN KEY (`agenzia_id`) REFERENCES `cms_agenzie` (`agenzia_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `cms_agenzie_orari` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `agenzia_id` char(4) NOT NULL,
  `day` tinyint(1) NOT NULL,
  `t1` TIME,
  `t2` TIME,
  `t3` TIME,
  `t4` TIME,
  `closed` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `cms_agenzie_orari_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `cms_agenzie` (`agenzia_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



DROP VIEW IF EXISTS `vw_cms_agenzie_backend`;
CREATE VIEW `vw_cms_agenzie_backend` as
SELECT ca.agenzia_id, ag.nome, ca.agencyEntrancePhoto, ca.whatsapp, ca.description, ca.publishable, ca.url, ca.pec, ca.text, gd.nome as distretto, ga.nome as area, ca.showWeeklySchedule
  FROM cms_agenzie ca
  JOIN agenzie ag ON ag.id = ca.agenzia_id
  JOIN geo_aree ga ON ag.area = ga.id
  JOIN geo_districts gd ON ag.district = gd.id;

DROP VIEW IF EXISTS `vw_cms_agenzie_impiegati`;
CREATE VIEW `vw_cms_agenzie_impiegati` as
  SELECT i.user_id, u.type, u.nome, u.cognome, u.agenzia_id, u.rui, u.email, u.ruolo, i.photo, i.position, i.showOnFrontend, i.privacy, i.description
  FROM cms_agenzie_impiegati i
    JOIN users u ON u.id = i.user_id
    WHERE u.active = 1;

-- Seed for testing
/*INSERT INTO `cms_agenzie` (`agenzia_id`, `agencyEntrancePhoto`, `whatsapp`, `pec`, `publishable`, `url`, `text`, `description`, `showWeeklySchedule`) VALUES ('G011', null, '3809043322', '<EMAIL>', '1', 'www.placeholder-g011.com', 'CHIUSURA STRAORDINARIA – Siamo chiusi lunedì 4 aprile per rinnovo locali. La nostra Agenzia è aperta dal lunedì al giovedì dalle 9 alle 18. Il venerdì e il sabato ti aspettiamo la mattina dalle 9 alle 13. Siamo chiusi in tutti i giorni festivi e semifestivi di calendario.', 'Dal 1983, abbiamo reso più tranquille le vite di oltre 20.000 persone, proteggendoli dagli imprevisti alla guida e difendendo la loro casa, famiglia e i loro risparmi. Lo abbiamo fatto grazie alla competenza dei nostri Agenti, alla loro correttezza e alla loro capacità di guidarti nella scelta della soluzione assicurativa più adatta alle tue esigenze. Ti aspettiamo da Rossi Assicurazioni, in via dei Gigli a Bergamo.', '1');
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`) VALUES ('G011', '120', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '1', 'YES', 'test placeholder descrizione Agente');
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`, `position`) VALUES ('G011', '8752', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '1', 'YES', 'test placeholder descrizione Intermediario', null);
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`, `position`) VALUES ('G011', '12934', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '0', 'UNSET', 'test placeholder descrizione Intermediario', null);
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`, `position`) VALUES ('G011', '21316', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '1', 'YES', 'test placeholder descrizione Intermediario', 'AUT');
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`, `position`) VALUES ('G011', '23794', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '0', 'YES', 'test placeholder descrizione Intermediario', null);
INSERT INTO `cms_agenzie_impiegati` (`agenzia_id`, `user_id`, `photo`, `showOnFrontend`, `privacy`, `description`, `position`) VALUES ('G011', '2913', '/cms-agenzie/upload/employees/G011/G011-mario-rossi.png', '1', 'YES', 'test placeholder descrizione Intermediario', 'PER');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '1', '09:00', '13:00', '15:00', '18:00', '0');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '2', '09:00', '13:00', '15:00', '18:00', '0');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '3', '09:00', '13:00', '15:00', '18:00', '0');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '4', '09:00', '13:00', '15:00', '18:00', '0');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '5', '09:00', '13:00', null, null, '0');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '6', null, null, null, null, '1');
INSERT INTO `cms_agenzie_orari` (`agenzia_id`, `day`, `t1`, `t2`, `t3`, `t4`, `closed`) VALUES ('G011', '7', null, null, null, null, '1');*/




--
-- Table structure for table `cms_agenzie_impiegati_mail`
--

DROP TABLE IF EXISTS `cms_agenzie_impiegati_mail`;
CREATE TABLE `cms_agenzie_impiegati_mail` (
  `id` mediumint UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` mediumint UNSIGNED NOT NULL,
  `email` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sender` mediumint UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `cms_agenzie_impiegati_mail_ibfk_1` FOREIGN KEY (`sender`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;