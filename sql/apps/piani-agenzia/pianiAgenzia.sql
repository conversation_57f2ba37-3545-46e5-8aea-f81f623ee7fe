-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *******
-- http://www.phpmyadmin.net
--
-- Host: localhost
-- Generato il: Giu 26, 2013 alle 14:54
-- Versione del server: 5.1.63
-- Versione PHP: 5.3.2-1ubuntu4.17

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- Database: `portaleagendo`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `pianiagz_agz_accordo`
--

DROP TABLE IF EXISTS `pianiagz_agz_accordo`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_accordo` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ramiPrefL1Premi` decimal(10,2) NOT NULL,
  `ramiPrefL1Proposta` decimal(10,2) DEFAULT NULL,
  `ramiPrefL2Premi` decimal(10,2) NOT NULL,
  `ramiPrefL2Proposta` decimal(10,2) DEFAULT NULL,
  `vitaL1NuovaProd` decimal(10,2) NOT NULL,
  `vitaL1Unici` decimal(10,2) NOT NULL,
  `vitaL1Annui` decimal(10,2) NOT NULL,
  `vitaL1Proposta` decimal(10,2) DEFAULT NULL,
  `vitaL1PropostaUnici` decimal(10,2) DEFAULT NULL,
  `vitaL1PropostaAnnui` decimal(10,2) DEFAULT NULL,
  `vitaL2NuovaProd` decimal(10,2) NOT NULL,
  `vitaL2Unici` decimal(10,2) NOT NULL,
  `vitaL2Annui` decimal(10,2) NOT NULL,
  `vitaL2Proposta` decimal(10,2) DEFAULT NULL,
  `vitaL2PropostaUnici` decimal(10,2) DEFAULT NULL,
  `vitaL2PropostaAnnui` decimal(10,2) DEFAULT NULL,
  `clientiPrivatiProposta` smallint(1) NOT NULL,
  `clientiPrivatiObj` smallint(1) DEFAULT NULL,
  `clientiAziendeProposta` smallint(1) NOT NULL,
  `clientiAziendeObj` smallint(1) DEFAULT NULL,
  `calcValues` varchar(4095) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1704 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_avanzamento`
--

DROP TABLE IF EXISTS `pianiagz_agz_avanzamento`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_avanzamento` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `year` year(4) NOT NULL,
  `month` tinyint(1) unsigned NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `ramiPrefIncassi` decimal(10,2) NOT NULL,
  `ramiPrefL1Rappel` decimal(10,2) NOT NULL,
  `ramiPrefL2Rappel` decimal(10,2) NOT NULL,
  `ramiPrefNuovaProd` decimal(10,2) NOT NULL,
  `vitaIncassi` decimal(10,2) NOT NULL,
  `vitaL1Rappel` decimal(10,2) NOT NULL,
  `vitaL2Rappel` decimal(10,2) NOT NULL,
  `vitaNuovaProdAnnui` decimal(10,2) NOT NULL,
  `vitaNuovaProdUnici` decimal(10,2) NOT NULL,
  `clientiTassoEqPrivati` decimal(5,2) NOT NULL,
  `clientiTassoEqAziende` decimal(5,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `year` (`year`,`month`,`agenzia_id`),
  KEY `agenzia_id` (`agenzia_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=2590 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_azione`
--

DROP TABLE IF EXISTS `pianiagz_agz_azione`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_azione` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `azione_id` mediumint(8) DEFAULT NULL,
  `type` enum('ACTION','FORECAST') NOT NULL,
  `famiglia` enum('SVILUPPO_RAMI_ELEMENTARI','SVILUPPO_VITA','RIENTRO_FLESSIBILITA','RECUPERO_MARGINE','POTENZIAMENTO_FORZA_VENDITA','RIORGANIZZAZIONE_FORZA_VENDITA','GESTIONE_TERRITORIALE','ACQUISIZIONE_NUOVI_CLIENTI','POTENZIAMENTO_CROSS_UP_SELLING','RAFFORZAMENTO_AGT_INT','ALTRO') NOT NULL,
  `status` enum('NEW','WORK_IN_PROGRESS','DONE','ABORTED','STANDBY') NOT NULL DEFAULT 'NEW',
  `titolo` varchar(128) NOT NULL,
  `body` varchar(1024) NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `azione` (`azione_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_consuntivo`
--

DROP TABLE IF EXISTS `pianiagz_agz_consuntivo`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_consuntivo` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `year` year(4) NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `ricaviProvvDanniVita` decimal(10,2) NOT NULL,
  `ricaviProvvAuto` decimal(10,2) NOT NULL,
  `ricaviProvvDanniNoAuto` decimal(10,2) NOT NULL,
  `ricaviProvvVita` decimal(10,2) NOT NULL,
  `ricaviContributiGare` decimal(10,2) NOT NULL,
  `ricaviRappel` decimal(10,2) NOT NULL,
  `ricaviTotale` decimal(10,2) NOT NULL,
  `redditPremi` decimal(10,2) NOT NULL,
  `redditProvvRappel` decimal(10,2) NOT NULL,
  `redditSinistri` decimal(10,2) NOT NULL,
  `redditSpese` decimal(10,2) NOT NULL,
  `redditCostiGestione` decimal(10,2) NOT NULL,
  `redditMargine` decimal(10,2) NOT NULL,
  `redditSP` decimal(4,2) NOT NULL,
  `redditCorrettivoC` decimal(10,2) NOT NULL,
  `prodzvAnnui` decimal(10,2) NOT NULL,
  `prodzvAnnui_idx` decimal(10,2) NOT NULL,
  `prodzvUnici` decimal(10,2) NOT NULL,
  `prodzvUnici_idx` decimal(10,2) NOT NULL,
  `prodzvTotale` decimal(10,2) NOT NULL,
  `prodzvTotale_idx` decimal(10,2) NOT NULL,
  `prodrpNuovaProd` decimal(10,2) NOT NULL,
  `clientNumAziende` smallint(1) unsigned NOT NULL,
  `clientNumPrivatiAuto` smallint(1) unsigned NOT NULL,
  `clientNumPrivatiVita` smallint(1) unsigned NOT NULL,
  `clientNumPrivatiTot` smallint(1) unsigned NOT NULL,
  `clientTassoEQAziende` decimal(4,2) NOT NULL,
  `clientTassoEQPrivati` decimal(4,2) NOT NULL,
  `clientC1` smallint(1) unsigned NOT NULL,
  `clientC2` smallint(1) unsigned NOT NULL,
  `clientC3` smallint(1) unsigned NOT NULL,
  `clientC4` smallint(1) unsigned NOT NULL,
  `clientC5` smallint(1) unsigned NOT NULL,
  `clientC6` smallint(1) unsigned NOT NULL,
  `competPtfAuto` decimal(10,2) NOT NULL,
  `competPtfRE` decimal(10,2) NOT NULL,
  `competPtfVita` decimal(10,2) NOT NULL,
  `competIncassi` decimal(10,2) NOT NULL,
  `competIQ` decimal(4,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `year` (`year`,`agenzia_id`),
  KEY `agenzia_id` (`agenzia_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=4248 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_locali`
--

DROP TABLE IF EXISTS `pianiagz_agz_locali`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_locali` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ubicazione` enum('CENTRO','LIMITROFE','PERIFERIA') NOT NULL,
  `posizione` enum('STRADA_VETRINA','PIANO_RIALZATO','PIANO_SUPERIORE') NOT NULL,
  `parcheggio` tinyint(1) NOT NULL,
  `insegna` enum('STRADA','VETROFANIA','PALINE_STRADALI','ALTRO') NOT NULL,
  `note` text NOT NULL,
  `giudizio` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=23 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_piani_azioni`
--

DROP TABLE IF EXISTS `pianiagz_agz_piani_azioni`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_piani_azioni` (
  `rev_id` mediumint(8) unsigned NOT NULL,
  `azione_id` mediumint(8) NOT NULL,
  PRIMARY KEY (`rev_id`,`azione_id`),
  KEY `azione_id` (`azione_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_piano`
--

DROP TABLE IF EXISTS `pianiagz_agz_piano`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_piano` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `year` year(4) NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `stato` enum('DRAFT','ONLINE') NOT NULL DEFAULT 'DRAFT',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ultimoAggiornamento` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `year` (`year`,`agenzia_id`),
  KEY `agenzia_id` (`agenzia_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=5067 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_rev`
--

DROP TABLE IF EXISTS `pianiagz_agz_rev`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_rev` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `piano_id` mediumint(8) unsigned NOT NULL,
  `user_id` mediumint(8) unsigned DEFAULT NULL,
  `locali_id` mediumint(8) unsigned DEFAULT NULL,
  `servizio_id` mediumint(8) unsigned DEFAULT NULL,
  `accordo_id` mediumint(8) unsigned DEFAULT NULL,
  `type` enum('WORKING_COPY','FROZEN') NOT NULL,
  `note` text,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `locali_id` (`locali_id`),
  KEY `servizio_id` (`servizio_id`),
  KEY `accordo_id` (`accordo_id`),
  KEY `piano_id` (`piano_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=5072 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_agz_servizio`
--

DROP TABLE IF EXISTS `pianiagz_agz_servizio`;
CREATE TABLE IF NOT EXISTS `pianiagz_agz_servizio` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `incassoCliente` tinyint(1) NOT NULL DEFAULT '0',
  `posInterno` tinyint(1) NOT NULL DEFAULT '0',
  `posEsterno` tinyint(1) NOT NULL DEFAULT '0',
  `orariFunzionali` enum('MATTINA','POMERIGGIO','PAUSA_PRANZO','CONTINUATO') DEFAULT NULL,
  `reperibAgente` varchar(12) DEFAULT NULL,
  `aggiornSinistro` tinyint(1) NOT NULL DEFAULT '0',
  `convenzEserciziLocali` tinyint(1) NOT NULL DEFAULT '0',
  `crm` tinyint(1) NOT NULL DEFAULT '0',
  `web` varchar(127) NOT NULL DEFAULT '',
  `segrTelefoniche` tinyint(1) NOT NULL DEFAULT '0',
  `note` text NOT NULL,
  `giudizioAgenzia` tinyint(1) DEFAULT NULL,
  `giudizioSubagenzia` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=27 ;

-- --------------------------------------------------------

--
-- Struttura della tabella `pianiagz_prov_consuntivo`
--

DROP TABLE IF EXISTS `pianiagz_prov_consuntivo`;
CREATE TABLE IF NOT EXISTS `pianiagz_prov_consuntivo` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT,
  `year` year(4) NOT NULL,
  `provincia` char(2) NOT NULL,
  `compagniaPtfAuto` decimal(10,2) NOT NULL,
  `compagniaPtfRE` decimal(10,2) NOT NULL,
  `compagniaPtfVita` decimal(10,2) NOT NULL,
  `compagniaIncassi` decimal(10,2) NOT NULL,
  `compagniaIQ` decimal(4,2) NOT NULL,
  `mercatoPtfAuto` decimal(10,2) NOT NULL,
  `mercatoPtfRE` decimal(10,2) NOT NULL,
  `mercatoPtfVita` decimal(10,2) NOT NULL,
  `mercatoIncassi` decimal(10,2) NOT NULL,
  `mercatoIQ` decimal(4,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `year` (`year`,`provincia`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=541 ;

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `pianiagz_agz_avanzamento`
--
ALTER TABLE `pianiagz_agz_avanzamento`
  ADD CONSTRAINT `pianiagz_agz_avanzamento_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `pianiagz_agz_azione`
--
ALTER TABLE `pianiagz_agz_azione`
  ADD CONSTRAINT `pianiagz_agz_azione_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_azione_ibfk_2` FOREIGN KEY (`azione_id`) REFERENCES `pianiagz_agz_azione` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `pianiagz_agz_consuntivo`
--
ALTER TABLE `pianiagz_agz_consuntivo`
  ADD CONSTRAINT `pianiagz_agz_consuntivo_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Limiti per la tabella `pianiagz_agz_piani_azioni`
--
ALTER TABLE `pianiagz_agz_piani_azioni`
  ADD CONSTRAINT `pianiagz_agz_piani_azioni_ibfk_1` FOREIGN KEY (`rev_id`) REFERENCES `pianiagz_agz_rev` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_piani_azioni_ibfk_2` FOREIGN KEY (`azione_id`) REFERENCES `pianiagz_agz_azione` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `pianiagz_agz_piano`
--
ALTER TABLE `pianiagz_agz_piano`
  ADD CONSTRAINT `pianiagz_agz_piano_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`) ON DELETE NO ACTION;

--
-- Limiti per la tabella `pianiagz_agz_rev`
--
ALTER TABLE `pianiagz_agz_rev`
  ADD CONSTRAINT `pianiagz_agz_rev_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_rev_ibfk_3` FOREIGN KEY (`locali_id`) REFERENCES `pianiagz_agz_locali` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_rev_ibfk_4` FOREIGN KEY (`servizio_id`) REFERENCES `pianiagz_agz_servizio` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_rev_ibfk_6` FOREIGN KEY (`accordo_id`) REFERENCES `pianiagz_agz_accordo` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `pianiagz_agz_rev_ibfk_7` FOREIGN KEY (`piano_id`) REFERENCES `pianiagz_agz_piano` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
