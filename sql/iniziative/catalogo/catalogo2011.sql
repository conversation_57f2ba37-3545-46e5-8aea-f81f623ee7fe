SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";


DROP TABLE IF EXISTS `catalogo2011_premi`;
CREATE TABLE IF NOT EXISTS `catalogo2011_premi` (
	`id`				tinyint unsigned NOT NULL AUTO_INCREMENT,
	`codice`			varchar(8) NOT NULL,				/* Codice premio x la gara */
	`codiceArticolo`	varchar(100) NOT NULL,				/* Codice articolo x Promotica */
	`nome`				varchar(50) NOT NULL,
	`marca`				varchar(50) NOT NULL,
	`descrizione`		text NOT NULL,
	`categoria`			smallint unsigned NOT NULL default 1,
	`punti`				smallint unsigned NOT NULL default 0,
	`status`			ENUM('0','1') NOT NULL default '0',
	`flags`				ENUM('CS') NULL default NULL,
	`imgUrl`			varchar(100) NOT NULL,
	`thumbUrl`			varchar(100) NOT NULL,
	`updatedAt`			timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	<PERSON>IMARY KEY (`id`),
	UNIQUE KEY (`codice`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Catalogo 2011 Premi';


DROP TABLE IF EXISTS `catalogo2011_ordini`;
CREATE TABLE IF NOT EXISTS `catalogo2011_ordini` (
	`id`				mediumint unsigned NOT NULL AUTO_INCREMENT,
	`premio_id`			tinyint(2) unsigned zerofill NOT NULL,
	`user_id`			mediumint unsigned NOT NULL,
	`status`			ENUM('1','2','3','4') NOT NULL default '1',
	`name`				varchar(50) NOT NULL,
	`surname`			varchar(50) NOT NULL,
	`agenzia`			varchar(50) NOT NULL,
	`email`				varchar(50) NOT NULL,
	`phone`				varchar(15) NOT NULL,
	`address`			varchar(150) NOT NULL,
	`city`				varchar(50) NOT NULL,
	`postalCode`		char(5) NOT NULL,
	`state`				char(2) NOT NULL						COMMENT 'Provincia',
	`opPersonaRif`		varchar(50) NOT NULL,
	`opGiornoChiusura`	ENUM('0','1','2','3','4','5','6','7') NOT NULL default '0',
	`opPiano`			tinyint unsigned NOT NULL default 0,
	`opAscensore`		ENUM('0','1') NOT NULL default '0',
	`opZonaPedonale`	ENUM('0','1') NOT NULL default '0',
	`opZTL`				ENUM('0','1') NOT NULL default '0',
	`opMercato`			ENUM('0','1','2','3','4','5','6','7') NOT NULL default '0',
	`opNote`			varchar(255),
	`createdAt`			datetime NOT NULL,
	`updatedAt`			timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='2011 Ordini';
ALTER TABLE `catalogo2011_ordini`
	ADD CONSTRAINT `fk_catalogo2011ordini_premi` FOREIGN KEY (`premio_id`) REFERENCES `catalogo2011_premi` (`id`),
	ADD CONSTRAINT `fk_catalogo2011ordini_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
;


DROP VIEW IF EXISTS `vw_catalogo2011_ordini`;
CREATE VIEW `vw_catalogo2011_ordini` AS
SELECT
	o.*,
	p.codice	AS premioCodice,
	p.nome		AS premioNome,
	p.marca		AS premioMarca,
	p.punti		AS premioPunti,
	u.agenzia_id AS agenzia_id,
	CONCAT(u.cognome, ' ',u.nome) AS userFullName
FROM
	`catalogo2011_ordini` o
	LEFT JOIN `users` u ON ( o.user_id = u.id )
	LEFT JOIN `catalogo2011_premi` p ON ( o.premio_id = p.id )
;


SET FOREIGN_KEY_CHECKS=1;

/* ======================= TRIGGERS =========================================== */

DROP TRIGGER IF EXISTS `triggerAI_catalogo2011ordini`;
DELIMITER //
CREATE TRIGGER `triggerAI_catalogo2011ordini` AFTER INSERT ON `catalogo2011_ordini`
	FOR EACH ROW BEGIN
		CALL `iniz_49_set_user_points`(NEW.user_id);
	END; //
DELIMITER ;
