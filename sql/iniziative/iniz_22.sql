/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS `iniz_22`;
CREATE TABLE IF NOT EXISTS `iniz_22` (
	`agenzia_id`						char(4) NOT NULL,
	`numeroPolizze`					MEDIUMINT unsigned NOT NULL default 0,
	`totPremiComputabili`			DECIMAL(12,2) unsigned NOT NULL COMMENT 'xtype=MONETARY',
	`bonus`							SMALLINT unsigned NOT NULL default 0,
	`importoErogabile`				DECIMAL(12,2) unsigned NOT NULL COMMENT 'xtype=MONETARY',
	PRIMARY KEY (`agenzia_id`),
	CONSTRAINT `fk_iniz22` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS `vw_iniz_22`;
CREATE VIEW `vw_iniz_22` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.bonus,
	st.importoErogabile
FROM
	`iniz_22` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */


DROP PROCEDURE IF EXISTS `iniz_22_set_status`;
DELIMITER //
CREATE PROCEDURE `iniz_22_set_status` (
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT default 22;
	DECLARE done 							INT default 0;
	DECLARE v_agenzia_id						CHAR(4);
	DECLARE C_BONUS1						MEDIUMINT;
	DECLARE C_BONUS2						MEDIUMINT;
	DECLARE C_OBIETTIVO1					MEDIUMINT;
	DECLARE C_OBIETTIVO2					MEDIUMINT;
	DECLARE v_numeroPolizze					MEDIUMINT;
	DECLARE v_totPremiComputabili			DECIMAL(12,2);
	DECLARE v_bonus							SMALLINT;
	DECLARE v_importoErogabile				DECIMAL(12,2);
	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT `agenzia_id` FROM `iniz_22` ORDER BY `agenzia_id`;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	/* PROCEDURE */
	SET C_INIZIATIVA_ID = 22;
	SET C_OBIETTIVO1 = 5;
	SET C_OBIETTIVO2 = 12;
	SET C_BONUS1 = 10;
	SET C_BONUS2 = 15;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		/* set default values */
		SET v_numeroPolizze = 0; SET v_totPremiComputabili = 0;
		SET v_bonus = 0; SET v_importoErogabile = 0;

		/* get other values */
		SELECT COUNT(*) INTO v_numeroPolizze
			FROM `iniz_polizze` WHERE iniziativa_id = C_INIZIATIVA_ID AND `agenzia_id` = v_agenzia_id;
		IF v_numeroPolizze IS NULL THEN SET v_numeroPolizze = 0; END IF;
		SELECT SUM(`premioComputabile`) INTO v_totPremiComputabili
			FROM `iniz_polizze` WHERE `iniziativa_id` = C_INIZIATIVA_ID AND `agenzia_id` = v_agenzia_id;
		IF v_totPremiComputabili IS NULL THEN SET v_totPremiComputabili = 0; END IF;

		/* calculate */
		IF		v_numeroPolizze >= C_OBIETTIVO2		THEN SET v_bonus = C_BONUS2;
		ELSEIF	v_numeroPolizze >= C_OBIETTIVO1		THEN SET v_bonus = C_BONUS1;
		END IF;
		SET v_importoErogabile = v_totPremiComputabili * v_bonus / 100;

		/* update tables */
		UPDATE `iniz_22` SET
			numeroPolizze = v_numeroPolizze,
			totPremiComputabili = v_totPremiComputabili, bonus = v_bonus, importoErogabile = v_importoErogabile
			WHERE `agenzia_id` = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;

