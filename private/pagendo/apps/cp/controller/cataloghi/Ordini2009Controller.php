<?php
namespace pagendo\apps\cp\controller\cataloghi;
use org\metadigit\web\Request,
	org\metadigit\web\Response,
	org\metadigit\web\view\JsonView,
	org\metadigit\web\view\PhpView,
	org\metadigit\session\SessionManager,
	pagendo\model\catalogo2009\OrdineVW,
	pagendo\model\catalogo2009\OrderManager,
	pagendo\model\catalogo2009\OrderManagerException;

class Ordini2009Controller extends \com\metadigit\extjs2\mvc\ModuleController {

	const MASK='1000010';

	function index(Request $Req, Response $Res) {
		$Admin = $_SESSION['User'];
		$acl = (SessionManager::checkACL(self::MASK,'CP')) ? '':'x-hidden';
		// set Model&View
		$Res->set('editACL', $acl);
		$Res->set('adminGroup', $Admin->type);
		return '/cataloghi/Ordini2009';
	}

	function changeOrderStatus(Request $Req, Response $Res) {
		$Admin = $_SESSION['User'];
		$success=false;
		try{
			$success = OrderManager::changeOrderStatus($Req->order_id,(int)$Req->status,$Admin);
		}catch(OrderManagerException $Ex){
			$err = 'Cambio di stato NON AUTORIZZATO!';
		}
		// build Response
		if($success) $response = array('success'=>true);
		else $response = array('success'=>false,'errors'=>array('reason'=>$err));
		// set Model & View
		$Res->setAttributes($response);
		return new JsonView;
	}

	function downloadXLS(Request $Req, Response $Res) {
		// get data
		$items = OrdineVW::fetchAllBy(array(),array(array('id','ASC')));
		// set Model&View
		$Res->set('data',	$items);
		$Res->set('filename',	'Ordini2009');
		$Res->set('filedate',	date('Y-m-d-Hi'));
		$View = new PhpView;
		$View->setTemplate(__DIR__.'/../../templates/cataloghi/Ordini2010.xls');
		return $View;
	}
}