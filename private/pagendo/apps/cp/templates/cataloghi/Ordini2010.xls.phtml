<?php
use com\metadigit\util\excel\QuickExcel;

header('Content-type: application/vnd.ms-excel');
header('Content-disposition: attachment; filename='.$filename.'-'.$filedate.'.xls');

$Excel = new QuickExcel;
$Excel->setData($data)
	->setIterator(QuickExcel::OBJECT_ITERATOR)
	->addColumn('ID', 'id')
	->addColumn('Data ordine', 'createdAt')
	->addColumn('Status', '_status')
	->addColumn('Cod. Premio', 'premioCodice')
	->addColumn('Punti', 'premioPunti')
	->addColumn('Nome premio', 'premioNome')
	->addColumn('Marca premio', 'premioMarca')
	->addColumn('Agenzia', 'agenzia_id')
	->addColumn('Nome', 'name')
	->addColumn('Cognome', 'surname')
	->addColumn('Cellulare', 'phone')
	->addColumn('Email', 'email')
	->addColumn('Indirizzo', 'address')
	->addColumn('Città', 'city')
	->addColumn('CAP', 'postalCode')
	->addColumn('Provincia', 'state')
	->addColumn('GiornoChiusura', '_opGiornoChiusura')
	->addColumn('Piano', 'opPiano')
	->addColumn('Ascensore', '_opAscensore')
	->addColumn('ZonaPedonale', '_opZonaPedonale')
	->addColumn('ZTL', '_opZTL')
	->addColumn('Mercato', '_opMercato')
	->addColumn('Note', 'opNote');
$Excel->write('php://output');