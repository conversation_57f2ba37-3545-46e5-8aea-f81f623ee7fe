(function(){
Ext.namespace($createNameSpace);
// create application
$NS = function() {

	// private variables & functions

	// ======== Data STORES ===============

	function euMoney(v){
		eur = v.formatMoney(2,',','.');
		if(v >= 1) return eur;
		else return '<font color="red">'+eur+'</font>';
	};
	function renderBonus(v){
		if(v == 10) return '<font color="green">'+v+'%</font>';
		else if(v == 8) return '<font color="orange">'+v+'%</font>';
		else return '<font color="red">'+v+'%</font>';
	};
	function renderPezzi(v){
		if(v >= 1) return v;
		else return '<font color="red">'+v+'</font>';
	};
	var StatusAgenzieSTORE = new Ext.data.JsonStore({
		url: '/iniz-api/32/all.json', totalProperty: 'total', root: 'data', baseParams: { 'limit':30 }, sortInfo: {field: 'agenzia_id', direction: 'ASC'}, autoLoad: true, remoteSort: true,
		fields: [
		{ name: 'agenzia_id' },
			{name: 'area'},
			{name: 'district'},
			{name: 'localita'},
			{name: 'nome'},
			{name: 'status'},
			{name: 'numeroPolizze',	type: 'int'},
			{name: 'totPremiComputabili', type: 'float'},
			{name: 'bonus',	type: 'int'},
			{name: 'importoErogabile',	type: 'float'}
		]
	});
	var StatusAgenzieCOLMODEL = new Ext.grid.ColumnModel([
		{header: 'ID', 	dataIndex: 'agenzia_id',				width: 50, sortable: true},
		{header: 'Località',		dataIndex: 'localita',			width: 150, sortable: true},
		{header: 'Nome',			dataIndex: 'nome',				width: 200, sortable: true},
		{header: 'Area',			dataIndex: 'area',		width: 50, sortable: true, align: 'right'},
		{header: 'District',		dataIndex: 'district',	width: 50, sortable: true, align: 'right'},
		{header: 'Status',			dataIndex: 'status',			tooltip: 'Status:<br/>ON: attiva<br/>OFF: chiusa<br/>DIREZ: direzione<br/>INTER: interinale', width: 40, sortable: true, align: 'center'},
		{header: 'Polizze',			dataIndex: 'numeroPolizze',		tooltip: 'Numero polizze', width: 50, sortable: true, align: 'right', renderer: renderPezzi},
		{header: 'Premi Comput.',	dataIndex: 'totPremiComputabili',	tooltip: 'Totale premi computabili',	width:  90, sortable: true, align: 'right', renderer: euMoney},
		{header: 'Bonus',			dataIndex: 'bonus',				tooltip: '% Bonus',					width:  50, sortable: true, align: 'right', renderer: renderBonus},
		{header: '€ liquid.',		dataIndex: 'importoErogabile',	tooltip: 'Incentivazione liquidabile',		width:  60, sortable: true, align: 'right', renderer: euMoney}
	]);
	var AggregatiSTORE = new Ext.data.JsonStore({
		url: '/iniz-api/32/summary.json', totalProperty: 'total', root: 'data', autoLoad: true, remoteSort: false,
		fields: [
			{name: 'status'},
			{name: 'area'},
			{name: 'district'},
			{name: 'numAgenzie', type: 'int'},
			//{name: 'totObiettivoOK', type: 'int'},
			{name: 'totPolizze', type: 'int'},
			{name: 'totPremiComputabili', type: 'float'},
			{name: 'totImportoErogabile', type: 'float'}
		]
	});
	var AggregatiCOLMODEL = new Ext.grid.ColumnModel({
		defaults: { sortable: false },
		columns: [
			{header: 'Status',			dataIndex: 'status',				tooltip: 'Status:<br/>ON: attiva<br/>OFF: chiusa<br/>DIREZ: direzione<br/>INTER: interinale', width: 50, align: 'center'},
			{header: 'Area',			dataIndex: 'area',			width: 50, align: 'right'},
			{header: 'District',		dataIndex: 'district',		width: 50, align: 'right'},
			{header: 'N° Agenzie',		dataIndex: 'numAgenzie',			width: 60, align: 'right'},
			//{header: 'Obiet. OK',		dataIndex: 'totObiettivoOK',		tooltip: 'totObiettivoOK', width: 60, align: 'right', renderer: renderPezzi},
			{header: 'N° Polizze',		dataIndex: 'totPolizze',			width: 60, align: 'right'},
			{header: 'TOT Premi Comp.',	dataIndex: 'totPremiComputabili',	tooltip: 'TOT Premi Computabili',		width:  100, align: 'right', renderer: euMoney},
			{header: 'TOT € erog.',		dataIndex: 'totImportoErogabile',	tooltip: 'Incentivazione liquidabile',	width:  100, align: 'right', renderer: euMoney}
		]
	});

	// ======== data GRID ===============

	var DataGRID = new Ext.grid.GridPanel({
		store: AggregatiSTORE, colModel: AggregatiCOLMODEL, stripeRows: true, loadMask: true,
		tbar: [
				'<b>Visualizza:</b> ',
				{ text: 'Aggregati', iconCls: 'silk-table', pressed: true, enableToggle: true, allowDepress: false, toggleGroup: 'ViewToggle', toggleHandler: showAggregati },
				' ',{ text: 'Dettaglio Agenzie', iconCls: 'silk-table', enableToggle: true, allowDepress: false, toggleGroup: 'ViewToggle', toggleHandler: showDettaglio },
				'-','<b>Scarica:</b> ',
				{ text: 'Aggregati', iconCls: 'silk-page_excel', handler: function(){ document.location='/iniz-api/32/summary.xls'; } },
				' ',{ text: 'Dettaglio Agenzie', iconCls: 'silk-page_excel', handler: function(){ document.location='/iniz-api/32/all.xls'; } },
				'->','-','Ultimo aggiornamento: <b>$updated</b> '
			],
		bbar: new Ext.PagingToolbar({
			disabled: true, store: StatusAgenzieSTORE, pageSize: 30, displayInfo: true
		}),
		viewConfig: {
			getRowClass: function(rec, i, rp) {
				switch(rec.get('status')){
					case 'ON':
						if(rec.get('area') == null) rp.tstyle = 'background-color:#99F299';
						else if(rec.get('district') == null) rp.tstyle = 'background-color:#E1FFCD';
						else rp.tstyle = '';
						break;
					case 'OFF':
						if(rec.get('area') == null) rp.tstyle = 'background-color:#D2B3FF';
						else if(rec.get('district') == null) rp.tstyle = 'background-color:#EDE3FF';
						else rp.tstyle = '';
						break;
					case 'DIREZ':
						if(rec.get('area') == null) rp.tstyle = 'background-color:#A2F0FF';
						else if(rec.get('district') == null) rp.tstyle = 'background-color:#DEFFFF';
						else rp.tstyle = '';
						break;
					default:
						rp.tstyle = 'background-color:#FFCC00';
				}
			}
		}
	});

	function showAggregati(){
		DataGRID.reconfigure(AggregatiSTORE, AggregatiCOLMODEL);
		DataGRID.getBottomToolbar().disable();
		DataGRID.doLayout(false, true);
	};
	function showDettaglio(){
		DataGRID.reconfigure(StatusAgenzieSTORE, StatusAgenzieCOLMODEL);
		DataGRID.getBottomToolbar().enable();
		DataGRID.doLayout(false, true);
	};

	// public space
	return {
		// public properties

		// public methods
		panel: function() {
			return DataGRID;
		},
		run: function(){}
    };
}();//end namespace

return $NS;
})();
