<?php
namespace pagendo\apps\formazcp\controller;
use pagendo\model\formaz\aree\Manager;
use pagendo\model\formaz\IscrizioneManager;
use pagendo\util\gui\BasicErrorHandler;

use pagendo\model\formaz\Partecipazione;

use pagendo\model\formaz\aree\PartecipazioneArea;

use pagendo\model\formaz\parent\AbstractPartecipazione;

use pagendo\model\formaz\parent\AbstractCorso;

use pagendo\model\formaz\aree\CorsoArea;

use org\metadigit\web\Request;
use org\metadigit\web\Response;
use org\metadigit\http\Session;
use pagendo\apps\formazcp\model\HandleModel;
use \pagendo\model\User;
use org\metadigit\core\Core;

class
AreeController extends \org\metadigit\web\ActionController {
	protected $years;
	protected $selectedYear;
	protected $aree;
	protected $areeID;
	protected $selectedArea;
	protected $navMenu;
	protected $pagerIpp;
	protected $pagerRequestPage;
	protected $pagerPages;
	protected $pagerOffset;
	protected $errorHandler;

    /**
     * @var Manager
     */
    protected $areeManager;

	protected function preHandle(Request $Req, Response $Res) {
		$this->years = $Req->years;
		$this->selectedYear = $Req->selectedYear;
		$this->aree = $Req->aree;
		$this->selectedArea = $Req->selectedArea;
		foreach ($this->aree as $key=>$area) {
			$this->areeID[] = $key;
		}
		$this->initNavMenu();
		$this->initPager($Req);
		$this->errorHandler = new BasicErrorHandler();
		$Res->set('formazContext', 'aree');
		$Res->set("showExcel",false);
		$Res->set('js',array('aree.js','commonAree.js', 'corsiAree.js'));
		$Res->set('css',array('cssAree.css', 'jquery.jscrollpaneAree.css', 'corsiAree.css'));
		return true;
	}

	protected function postHandle(Request $Req, Response $Res, $View=null){
		$this->finalizePager($Res);
		$Res->set('navMenu',$this->navMenu);
		$Res->set('err',$this->errorHandler->renderAlert());
		//print "<pre>".print_r($Req,true)."</pre>";
		return true;
	}

	function index(Request $Req, Response $Res) {
		return $this->elenco($Req,$Res);
	}

	function step1(Request $Req, Response $Res) {
		$corso = null;
		if ($Req->id!=null)
			$corso = CorsoArea::fetch($Req->id);
		if ($corso != null) {
			$this->setNavMenuYear('text');
			$this->setNavMenuArea('text');
			$this->setNavMenuItem("Setup Corso", null, 'active','smallArrow');
			$this->setNavMenuItem("Step 1 &raquo; Info Generali", null, 'active',1,'mediumArrow');
			$this->setNavMenuItem("Step 2 &raquo; Partecipanti", "/ControlPanel/Formazione/aree/partecipanti?corso_id={$corso->id}", 'off',2,'mediumArrow');
			if (strlen($corso->protocollo)>0) {
				$prefissoProtocollo = substr($corso->protocollo,0,-3);
				$corso->protocollo = substr($corso->protocollo,-3);
				$Res->set("prefissoProtocollo",$prefissoProtocollo);
			} else {
				$Res->set("prefissoProtocollo",CorsoArea::getProtocollo($this->selectedArea, $this->selectedYear));
			}
			$Res->set("mode","edit");
			$Res->set("corso",$corso);
			$Res->set("dTitle","Modifica corso &raquo; ");
			$Res->set("tipologie",AbstractCorso::getTypes());
			$Res->set("strutture",AbstractCorso::getStructures());
			$Res->set("modalita",AbstractCorso::getMode());
		} else {
			$criteria [] = "anno={$this->selectedYear}";
			$criteria [] = "impl='aree'";
			$corsi = AbstractCorso::fetchAllBy($criteria, ['titolo asc']);
			$this->setNavMenuItem("Setup Corso", null, 'active','smallArrow');
			$this->setNavMenuItem("Step 1 &raquo; Info Generali", null, 'active',1,'mediumArrow');
			$this->setNavMenuItem("Step 2 &raquo; Partecipanti", null, 'off',2,'mediumArrow');
			$Res->set("mode","insert");
			$Res->set("corsi",$corsi);
			$Res->set("prefissoProtocollo",CorsoArea::getProtocollo($this->selectedArea, $this->selectedYear));
			$Res->set("dTitle","Setup nuovo corso &raquo; ");
		}
		return "step1";
	}

	function partecipanti(Request $Req, Response $Res) {
		$criteria [] = "corso_area_id={$Req->corso_id}";
		if ($Req->export!=null) {
			$partecipanti = PartecipazioneArea::fetchAllBy($criteria);
		} else {
			$partecipanti = PartecipazioneArea::fetchAllBy($criteria,null,$this->pagerIpp,$this->pagerOffset);
		}
		$this->updatePager(PartecipazioneArea::countBy($criteria));
		$corso = CorsoArea::fetch($Req->corso_id);
		$libs = new \pagendo\util\gui\ClientLibrariesManager();
		$libs->addLibrary('autocomplete');
		$this->setNavMenuYear('text');
		$this->setNavMenuArea('text');
		$this->setNavMenuItem("Setup Corso", null, 'active');
		$this->setNavMenuItem("Step 1 &raquo; Info Generali", "/ControlPanel/Formazione/aree/step1?id={$Req->corso_id}", 'off',1,'mediumArrow');
		$this->setNavMenuItem("Step 2 &raquo; Partecipanti", null, 'active',2,'mediumArrow');
		$Res->set('ClientLibraries',$libs);
		$Res->set("partecipanti",$partecipanti);
		$Res->set("corso",$corso);
		$Res->set("dTitle","Elenco iscritti &raquo; ");
		$Res->set("showExcel",true);
        $Res->set('verifier', $this->areeManager->getUserType($_SESSION['AUTH']['UTYPE']));

		if ($Req->export!=null) {
			return "partecipantiExport";
		}
		return "partecipanti";
	}

	function elenco(Request $Req, Response $Res) {
		if ($Req->status==null || $Req->status==0) {
			$function = "fetchAttiviByAnno";
			$this->setNavMenuItem("Gestione Corsi Attivi", "/ControlPanel/Formazione/aree/elenco", 'active');
			$Res->set("dTitle","Gestione attivi &raquo; ");
			$Res->set("status",0);
			$Req->status = 0;
		} else if ($Req->status!=null && $Req->status==1) {
			$function = "fetchAnnullatiByAnno";
			$this->setNavMenuItem("Archivio Annullati", "/ControlPanel/Formazione/aree/elenco?status=1", 'active');
			$Res->set("dTitle","Corsi annullati &raquo; ");
			$Res->set("status",1);
		} else if ($Req->status!=null && $Req->status==2) {
			$function = "fetchArchiviatiByAnno";
			$this->setNavMenuItem("Archivio Corsi", "/ControlPanel/Formazione/aree/elenco?status=2", 'active');
			$Res->set("dTitle","Corsi archiviati &raquo; ");
			$Res->set("status",2);
		}
		if ($Req->export!=null) {
			list($corsi,$count) = CorsoArea::$function($this->selectedYear,array($this->selectedArea), 10000);
		} else {
			list($corsi,$count) = CorsoArea::$function($this->selectedYear,array($this->selectedArea),$this->pagerIpp,$this->pagerOffset);
		}
		$this->updatePager($count);
		$Res->set("status",$Req->status);
		$Res->set("corsi",$corsi);
		$Res->set("showExcel",true);

		if ($Req->export!=null) {
			$meta = array(
				array (
					'title' => "Corsi attivi",
					'filename' => 'GestioneAttivi',
				),
				array (
					'title' => "Corsi annullati",
					'filename' => 'CorsiAnnullati',
				),
				array (
					'title' => "Corsi archiviati",
					'filename' => 'CorsiArchiviati',
				),
			);
			$Res->set("aree",$this->aree);
			$Res->set("title",$meta[$Req->status]['title']);
			$Res->set("filename",$meta[$Req->status]['filename']);

			return "elencoExport";
		}
		return "elenco";
	}

	function elencoCorsiDisponibili(Request $Req, Response $Res) {
		$criteria [] = "impl='aree'";
		$criteria [] = "anno={$this->selectedYear}";
        $orderBy = array("titolo asc");
		if ($Req->export!=null) {
			$corsi = AbstractCorso::fetchAllBy($criteria, $orderBy);
		} else {
			$corsi = AbstractCorso::fetchAllBy($criteria, $orderBy, $this->pagerIpp, $this->pagerOffset);
		}
		$this->updatePager(AbstractCorso::countBy($criteria));
		$this->setNavMenuItem("Corsi Per Aree", "/ControlPanel/Formazione/aree/elencoCorsiDisponibili", 'active');
		$Res->set("corsi",$corsi);
		$Res->set("tipologie",AbstractCorso::getTypes());
		$Res->set("strutture",AbstractCorso::getStructures());
		$Res->set("modalita",AbstractCorso::getMode());
		$Res->set("dTitle","Elenco corsi &raquo; ");
		$Res->set("showExcel",true);
		if ($Req->export!=null) {
			return "elencoDisponibiliExport";
		}
		return "elencoDisponibili";
	}

	function protocollo(Request $Req, Response $Res) {
		$data = CorsoArea::fetchAllByProtocolloAnnoArea($this->selectedYear, $this->selectedArea);
		$this->setNavMenuItem("Protocollo", "/ControlPanel/Formazione/aree/protocollo", 'active');
		$Res->set("data",$data);
		$Res->set("dTitle","Protocollo &raquo; ");
		$Res->set("showExcel",true);
		if ($Req->export!=null) {
			return "protocolloExport";
		}
		return "protocollo";
	}

	function dettaglioCorso(Request $Req, Response $Res) {
		$criteria [] = "corso_area_id={$Req->corso_id}";
		$partecipanti = PartecipazioneArea::fetchAllBy($criteria);
		//$this->updatePager(PartecipazioneArea::countBy($criteria));
		$corso = CorsoArea::fetch($Req->corso_id);
		$Res->set("partecipanti",$partecipanti);
		$Res->set("corso",$corso);
		if ($Req->export!=null) {
			return "partecipantiExport";
		}
		return "dettaglioCorso";
	}

    public function verifica(Request $Req, Response $Res) {
        $verifier = $this->areeManager->getUserType($_SESSION['AUTH']['UTYPE']);
        $partecipazione = PartecipazioneArea::fetch($Req->id);
        $Res->set('partecipazione', $partecipazione);
        $Res->set('verifier', $this->areeManager->getUserType($_SESSION['AUTH']['UTYPE']));
        $Res->set('strumenti', $verifier == 'admin' ? $partecipazione->flagAdminStrumenti : $partecipazione->flagAreaStrumenti);
        $Res->set('didattica', $verifier == 'admin' ? $partecipazione->flagAdminDidattica : $partecipazione->flagAreaDidattica);
        return "verify";
    }

    public function doVerifica(Request $Req, Response $Res) {
        //return $Res->sendRedirect("partecipanti?corso_id=$Req->corso_id");
        if (! $partecipazione = PartecipazioneArea::fetch($Req->id)) {
            Core::trace(LOG_DEBUG, 1, $this, __FUNCTION__, "Partecipazione not found: $Req->id");
            $Res->set('errors', "errors.partecipazione");
            return "verify";
        }

        if (! $year = $partecipazione->getParent()->getCorso()->anno) {
            Core::trace(LOG_DEBUG, 1, $this, __FUNCTION__, "Year not found: $Req->id");
            $Res->set('error', "errors.year");
            return "verify";
        }

        if (! $type = $_SESSION['AUTH']['UTYPE']) {
            Core::trace(LOG_DEBUG, 1, $this, __FUNCTION__, "User type not found");
            $Res->set('error', "errors.user");
            return "verify";
        }

        $post = $Req->getPostData();

        $result = $this->areeManager->setVerification(
            $year,
            $partecipazione,
            [
                'strumenti' => isset($post['strumenti']),
                'didattica' => isset($post['didattica']),
            ],
            $type
        );

        if (! $result) {
            $Res->set('error', "errors.save");
            return "verify";
        }

        return $Res->sendRedirect("partecipanti?corso_id=$Req->corso_id");
    }

	// =======================================================================================
	// Form Handlers
	// =======================================================================================
	function doInsertParent (Request $Req, Response $Res) {
		if (empty($_SESSION['FORMAZIONE']['AREE']['AdminAccess'])) {
			$Res->sendRedirect("index");
			return null;
		}
		CorsoArea::insertParent($this->selectedYear,$Req->getPostData());
		$Res->sendRedirect("addCorso?inserito=1");
		return null;
	}

	function doUpdateParent (Request $Req, Response $Res) {
		if (empty($_SESSION['FORMAZIONE']['AREE']['AdminAccess'])) {
			$Res->sendRedirect("index");
			return null;
		}
		CorsoArea::updateParent($this->selectedYear,$Req->getPostData());
		$Res->sendRedirect("addCorso?aggiornato=1");
		return null;
	}

	function doInsertInstance (Request $Req, Response $Res) {
		$post = $Req->getPostData();
		if (!empty($post['protocollo'])) {
			$post['protocollo'] = str_pad($post['protocollo'], 3, "0", STR_PAD_LEFT);
			$post['protocollo'] = $post['prefisso'] . $post['protocollo'];
		} else {
			$post['protocollo'] = '';
		}
		$post['data'] = $this->parseDate($post['data']);
		if ($post['data'] == null) {
			$this->errorHandler->push("La data non è stata inserita, o il formato non è valido.");
			$Res->sendRedirect("step1");
			return null;
		}
		if (empty($post['ore'])) {
			$this->errorHandler->push("Il campo 'Ore Formative' è obbligatorio.");
			$Res->sendRedirect("step1");
			return null;
		}
		$post['data2'] = $this->parseDate($post['data2']);
		$result = CorsoArea::insertInstance($this->selectedArea,$post);
		if (!empty($result))
			$Res->sendRedirect("partecipanti?corso_id={$result->id}");
		return null;
	}

	function doUpdateInstance (Request $Req, Response $Res) {
		$post = $Req->getPostData();
		$Corso = CorsoArea::fetch($post['id']);
		$post['data'] = $this->parseDate($post['data']);
		$post['data2'] = $this->parseDate($post['data2']);
		if (!empty($post['protocollo'])) {
			$post['protocollo'] = str_pad($post['protocollo'], 3, "0", STR_PAD_LEFT);
			$post['protocollo'] = $post['prefisso'] . $post['protocollo'];
		} else {
			$post['protocollo'] = '';
		}
		CorsoArea::updateInstance($Corso, $post);
		$Res->sendRedirect("step1?id={$post['id']}");
		return null;
	}

	function doRemoveInstance (Request $Req, Response $Res) {
		$post = $Req->getPostData();
		$corso = new AbstractCorso();
		$corso->id = $post['corso_id'];
		try {
			AbstractCorso::delete($corso);
		} catch (\Exception $ex) {
			Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,$ex->getMessage());
			$this->errorHandler->push("Impossibile eliminare questo titolo: verificare l'esistenza di eventuali corsi da Area.");
		}
		$Res->sendRedirect("elencoCorsiDisponibili");
		return null;
	}

	function doCancelCorso(Request $Req, Response $Res) {
		if ($Req->id != null) {
			$Corso = CorsoArea::fetch($Req->id);
			CorsoArea::cancelCorso($Corso);
		}
		return $this->elenco($Req,$Res);
	}

	function doTogglePratica(Request $Req, Response $Res) {
		if ($Req->id != null) {
			$Corso = CorsoArea::fetch($Req->id);
			if ($Corso->status == 'ON') {
				$Corso->status = 'ARC';
			} else if ($Corso->status == 'ARC') {
				$Corso->status = 'ON';
			}
			CorsoArea::update($Corso);
		}
		return $this->elenco($Req,$Res);
	}

	function doAddPartecipante(Request $Req, Response $Res) {
		$post = $Req->getPostData();
		$corso = CorsoArea::fetch($post['corso_id']);
		$abstractCorso = $corso->getParent();
		$user_id = $this->parseUserFromAutocomplete($post['user_id']);

		if ($user_id != null) {
			$abstractPartecipazione = new AbstractPartecipazione();
			$abstractPartecipazione->corso_id = $abstractCorso->id;
			$abstractPartecipazione->user_id = $user_id;
			$abstractPartecipazione->esito = 'ok';
			$abstractPartecipazione->oreFormative = $corso->oreFormative;
            $abstractPartecipazione->role = IscrizioneManager::getRole(User::fetch($user_id));
            $abstractPartecipazione->created = date(DATE_ISO8601);
			try {
				AbstractPartecipazione::insert($abstractPartecipazione);
				$partecipazione = new PartecipazioneArea();
				$partecipazione->partecipazione_id = $abstractPartecipazione->id;
				$partecipazione->corso_area_id = $corso->id;
				PartecipazioneArea::insert($partecipazione);
			} catch (\Exception $ex) {
				Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,$ex->getMessage());
				$this->errorHandler->push("Si � verificato un errore. Il nominativo richiesto � gi� stato invitato a questo corso?");
			}
		} else {
			Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Autocomplete: bad format: " . $post['user_id']);
			$this->errorHandler->push("Il nominativo richiesto non � stato trovato.");
		}
		$Res->sendRedirect("partecipanti?corso_id=" . $post['corso_id']);
		return null;
	}

	function doRemovePartecipante(Request $Req, Response $Res) {
		$post = $Req->getPostData();
		$partecipante = $post['partecipazione_id'];
		$parent = PartecipazioneArea::fetch($partecipante)->getParent();
		AbstractPartecipazione::delete($parent);
		if (isset($post['destination']) && $post['destination'] != null) {
			$Res->sendRedirect("{$post['destination']}?corso_id={$post['corso_id']}");
		} else {
			$Res->sendRedirect("partecipanti?corso_id={$post['corso_id']}");
		}
	}

	protected function parseUserFromAutocomplete ($input) {
		$input = explode(" ", $input);
		if (count($input) < 3)
			return null;

		$id = $input[0];
		$id = preg_replace("/\[|\]/", "", $id);
		$id = explode(":",$id);
		if (count($id)!=2)
			return null;

		return $id[1];
	}

	// =======================================================================================
	// Ajax Handlers
	// =======================================================================================
	function getAutocompleteUsers(Request $Req, Response $Res) {
		$q = addslashes($Req->q);
		$criteria [] = "cognome like '$q%'";
		$criteria [] = "active=1";
		$criteria [] = "type in ('AGENTE','INTERMEDIARIO')";
		if (!$_SESSION['FORMAZIONE']['AREE']['AdminAccess']) {
			if ($_SESSION['User']->type == 'AREAMGR') {
				$criteria [] = "area={$_SESSION['User']->area}";
			} else if ($_SESSION['User']->type == 'DISTRICTMGR') {
				$criteria [] = "district={$_SESSION['User']->district}";
			}
		}
		$users = User::fetchAllBy($criteria);
		foreach ($users as $user) {
			$agenzia = $user->getAgenzia();
			print "[id:{$user->id}] [{$user->type}".($agenzia!=null?"-".$agenzia->id:"")."] "
				  . utf8_encode($user->nome) . " "
				  . utf8_encode($user->cognome)
				  ."\n";
		}
		return null;
	}

	function addCorso(Request $Req, Response $Res) {
		$Res->set("tipologie",AbstractCorso::getTypes());
		$Res->set("strutture",AbstractCorso::getStructures());
		$Res->set("modalita",AbstractCorso::getMode());
		$Res->set("mode",'insert');
		return "addCorso";
	}

	function editCorso(Request $Req, Response $Res) {
		$Res->set("tipologie",AbstractCorso::getTypes());
		$Res->set("strutture",AbstractCorso::getStructures());
		$Res->set("modalita",AbstractCorso::getMode());
		$Res->set("corso",AbstractCorso::fetch($Req->id));
		$Res->set("mode",'edit');
		return "addCorso";
	}

    function setVerifiedAdmin(Request $Req, Response $Res) {
        $response = array('success' => FALSE);
        if ($Req->isPost()) {
            $post = $Req->getPostData();
            if ($post['id']) {
                $partecipazione = PartecipazioneArea::fetch($post['id']);
                $partecipazione->verifiedAdmin = $post['data'] ? 1 : 0;
                PartecipazioneArea::update($partecipazione);
                $response['success'] = TRUE;
            }
        }
        print json_encode($response);
        return NULL;
    }

    function setVerifiedArea(Request $Req, Response $Res) {
        $response = array('success' => FALSE);
        if ($Req->isPost()) {
            $post = $Req->getPostData();
            if ($post['id']) {
                $partecipazione = PartecipazioneArea::fetch($post['id']);
                $partecipazione->verifiedArea = 1;
                PartecipazioneArea::update($partecipazione);
                $response['success'] = TRUE;
            }
        }
        print json_encode($response);
        return NULL;
    }

    function setOreFormative(Request $Req, Response $Res) {
        $response = array('success' => FALSE);
        if ($Req->isPost()) {
            $post = $Req->getPostData();
            if ($post['id']) {
                $partecipazione = AbstractPartecipazione::fetch($post['id']);
                $partecipazione->oreFormative = $post['data'];
                AbstractPartecipazione::update($partecipazione);
                $response['success'] = TRUE;
            }
        }
        print json_encode($response);
        return NULL;
    }

	// =======================================================================================
	// Nav Handlers
	// =======================================================================================
	protected function initNavMenu() {
		$this->navMenu = array (
			'anno' => array(
				'type'=>'select',
				'data'=>$this->years,
				'selected'=>$this->selectedYear,
				'status'=>null,
				'href'=>null,
			),
			'aree' => array (
				'type'=>'select',
				'data'=>$this->aree,
				'selected'=>$this->selectedArea,
				'status'=>null,
				'href'=>null,
			),
			'nav' => array (),
		);
	}

	protected function setNavMenuYear($type,$status = 'off') {
		$this->navMenu['anno']['type'] = $type;
		$this->navMenu['anno']['status'] = $status;
	}

	protected function setNavMenuArea($type,$status = 'off') {
		$this->navMenu['aree']['type'] = $type;
		$this->navMenu['aree']['status'] = $status;
	}

	protected function setNavMenuItem($text,$href,$status,$index = 0,$type='smallArrow') {
		$this->navMenu['nav'][$index] = array (
			'text' => $text,
			'href' => $href,
			'status' => $status,
			'type'=> $type,
		);
	}

	// =======================================================================================
	// Pager Handlers
	// =======================================================================================
	protected function initPager($Req) {
		$this->pagerIpp = 15;
		$this->pagerRequestPage = $Req->page!=null?$Req->page-1:0;
		$this->pagerOffset = $this->pagerIpp * $this->pagerRequestPage;
	}

	protected function updatePager($items) {
		$this->pagerPages = ceil($items / $this->pagerIpp);
	}

	protected function finalizePager($Res) {
		if ($this->pagerPages>0) {
			$pager = array (
				'current' => $this->pagerRequestPage+1,
				'countPages' => $this->pagerPages
			);
			$Res->set("pager",$pager);
			$Res->set("showPagination",true);
		} else {
			$Res->set("showPagination",false);
		}
	}

	// =======================================================================================
	// Utils
	// =======================================================================================
	/**
	 *
	 * @param string $date
	 * @return string date in DATE_ISO8601 format
	 */
	protected function parseDate($date) {
		$date = explode("/",$date);
		if (count($date)<3)
			return null;
		if (strlen($date[0]) != 2 || strlen($date[1]) != 2 || strlen($date[2]) != 4)
			return null;
		$timestamp = mktime(0,0,0,$date[1],$date[0],$date[2]);
		if ($timestamp <= 0)
			return null;
		return date(DATE_ISO8601,$timestamp);
	}
}