
(function(compId){var _=null,y=true,n=false,x14='break-word',x112='176px',e36='${tcm}',x24='rgb(0, 0, 0)',x95='183px',x94='42px',x51='importoErogFondoPensione_TXT',i='none',x16='rgba(192,192,192,1)',e151='${pappagallo_messaggi_negativi_TCM}',x62='666px',x135='pappagallo_messaggi_negativi_TCM',x81='392px',e120='${pappagallo_messaggi_negativi_Unici}',x34='453px',x107='-12px',e76='${messaggio_pensione}',x82='112px',x17='rgba(255,255,255,1)',x113='228px',x83='128px',x54='224px',xc='rgba(0,0,0,1)',x1='6.0.0',x129='395px',x31='Unici',x2='5.0.0',x80='importoErogPremiUniciAnteBonus_TXT',x50='116px',x18='722px',x148='73px',x111='30px',x73='btn_pensione',x39='tabella_pensione',x103='20px',x133='rgba(217,212,37,1.00)',x159='122px',x118='chiudi_unici',x70='258px',x78='tabella_premi_unici',x46='pezziProgOpen_TXT',x108='pappagallo_messaggi_negativi_Unici',x23='Scurisce',o='opacity',x136='messaggio_tcm',x26='rgba(0,0,0,1.00)',x158='98px',e153='${messaggio_tcm}',x57='pappagallo_messaggi_positivi',x116='68px',x72='48px',x125='43px',x87='121px',x47='rgba(33,58,80,1)',x15='24',x27='-140px',x44='125px',x45='29px',x20='870px',x53='-9px',x13='700',e119='${messaggio_unici}',b='block',x141='589px',x156='443px',x85='premiUnici_TXT',x49='143px',e35='${Unici}',x48='417px',x98='rgba(241,149,167,1.00)',x140='260px',x52='rgba(255,255,255,1.00)',x59='pappagallo_messaggi_negativi',x56='visible',x3='6.0.0.400',tp='top',x64='181px',x63='36px',x86='46px',e161='${clicca_QUI}',x143='66px',x157='-23px',x117='50px',x42='60px',x131='41px',x22='auto',x155='1',x154='clicca_QUI',x67='Antonio-Bold',x134='pappagallo_messaggi_positivi_TCM',x110='668px',e77='${pappagallo_messaggi_negativi}',e152='${pappagallo_messaggi_positivi_TCM}',x68='400',x150='pallini_TCM',x149='135px',x33='770px',x21='470px',x104='rect(0px 335px 20px 0px)',x91='importoErogPremiUnici_TXT',x12='Calibri',x146='percTCM',x145='chiudi_tcm',x4='rgba(0,0,0,0)',x142='253px',x97='27px',x144='57px',g='image',po='center',x74='pointer',e121='${pappagallo_messaggi_positivi_Unici}',x138='24px',x127='123px',x29='453',x137='673px',x92='398px',x90='percBonusPU_TXT',x132='240px',x='text',d='display',x128='importoErogTcm_TXT',x96='480px',m='rect',x40='0px',x126='343px',x124='premiTCM_TXT',x122='tabella_TCM',x115='586px',x114='messaggio_unici',x19='-10px',x32='tcm',e75='${pappagallo_messaggi_positivi}',x93='slide_Unici',rt='right',x89='381px',x65='246px',x28='770',x100='44px',x84='rgba(33,58,80,1.00)',x88='importoErogPremiUniciAnteBonus2_TXT',x66='messaggio_pensione',e38='${Pensione}',x55='472px',l='normal',x101='288px',x99='pallini',x102='335px',x130='slide_TCM',x43='144px',x30='Pensione',x106='pappagallo_messaggi_positivi_Unici',e37='${Scurisce}',lf='left',x69='590px',x139='167px',x11='20',p='px',x25='0',x71='64px';var g105='pallini.svg',g60='pappagallo_messaggi_negativi.png',g5='Background.png',g6='drummer.gif',g41='tabella_pensione.png',g58='pappagallo_messaggi_positivi.png',g160='clicca_QUI.png',g123='tabella_TCM.png',g7='totem.gif',g79='tabella_premi_unici.png',g8='fondo_pensione.png',g9='hawwaiiana.gif';var s109="<p style=\"margin: 0px;\">​Aumenta la tua aliquota di incentivazione, ti mancano solo 985 euro!</p>",s10="<p style=\"margin: 0px;\">​0</p>",s61="<p style=\"margin: 0px;\">​sdasdfasdfsa</p>",s147="<p style=\"margin: 0px;\">​7,5%</p>";var im=absolutePath+'images/',aud='media/',vid='media/',js='js/',fonts={'calibri':'','Calibri':'','Antonio-Bold':'<link href=\"'+absolutePath+'fonts/stylesheet.css\" rel=\"stylesheet\">'},opts={'gAudioPreloadPreference':'auto','gVideoPreloadPreference':'auto'},resources=[],scripts=[],symbols={"stage":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{id:'Background',t:g,r:['0px','0px','869px','469px','auto','auto'],f:[x4,im+g5,'0px','0px']},{id:'drummer',t:g,r:['280px','101px','260px','334px','auto','auto'],f:[x4,im+g6,'0px','0px']},{id:'totem',t:g,r:['485px','52px','360px','400px','auto','auto'],f:[x4,im+g7,'0px','0px']},{id:'fondo_pensione',t:g,r:['401px','307px','113px','125px','auto','auto'],f:[x4,im+g8,'0px','0px']},{id:'hawwaiiana',t:g,r:['29px','105px','213px','334px','auto','auto'],f:[x4,im+g9,'0px','0px']},{id:'importoErogPremiUnici_HM',t:x,r:['225px','356px','75px','24px','auto','auto'],text:s10,align:"right",n:[x12,[x11,p],"rgba(255,255,255,1)",x13,i,l,x14,""],ts:["","","","",i]},{id:'importoErogFondoPensione_HM',t:x,r:['409px','407px','75px','24px','auto','auto'],text:s10,align:"right",n:[x12,[x11,p],"rgba(255,255,255,1)",x13,i,l,x14,""],ts:["","","","",i]},{id:'importoErogTcm_HM',t:x,r:['730px','348px','75px','24px','auto','auto'],text:s10,align:"right",n:[x12,[x11,p],"rgba(255,255,255,1)",x13,i,l,x14,""],ts:["","","","",i]},{id:'importoErogTotale_HM',t:x,r:['757px','203px','86px','29px','auto','auto'],text:s10,align:"right",n:[x12,[x15,p],"rgba(33,58,80,1.00)",x13,i,l,x14,""],ts:["","","","",i]},{id:'clicca',symbolName:'clicca',t:m,r:['272','163','98','122','auto','auto']},{id:'pulsante1',t:m,r:['476px','306px','40px','39px','auto','auto'],cu:'pointer',o:'0.0074074074074075',f:[x16],s:[0,xc,i]},{id:'pulsante2',t:m,r:['258px','260px','40px','39px','auto','auto'],cu:'pointer',o:'0',f:[x16],s:[0,"rgb(0, 0, 0)",i]},{id:'pulsante3',t:m,r:['743px','252px','40px','39px','auto','auto'],cu:'pointer',o:'0',f:[x16],s:[0,"rgb(0, 0, 0)",i]},{id:'Tabelle',symbolName:'Tabelle',t:m,r:['-723','8','770','453','auto','auto']}],style:{'${Stage}':{isStage:true,r:['null','null','869px','469px','auto','auto'],overflow:'hidden',f:[x17]}}},tt:{d:6000,a:y,data:[]}},"Tabelle":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{t:m,r:[x18,x19,x20,x21,x22,x22],v:i,id:x23,s:[0,x24,i],o:x25,f:[x26]},{r:[x27,x25,x28,x29,x22,x22],id:x30,sN:x30,t:m},{r:[x27,x25,x28,x29,x22,x22],id:x31,sN:x31,t:m},{r:[x27,x25,x28,x29,x22,x22],id:x32,sN:x32,t:m}],style:{'${symbolSelector}':{r:[_,_,x33,x34]}}},tt:{d:6000,a:y,data:[["eid30",lf,2000,1000,"easeOutQuad",e35,'0px','723px'],["eid37",lf,3000,1000,"easeOutQuad",e35,'723px','-140px'],["eid34",lf,4000,1000,"easeOutQuad",e36,'0px','723px'],["eid39",lf,5000,1000,"easeOutQuad",e36,'723px','-140px'],["eid254",d,0,0,"easeOutQuad",e37,i,i],["eid255",d,40,0,"easeOutQuad",e37,i,b],["eid256",d,2000,0,"easeOutQuad",e37,b,i],["eid257",d,2040,0,"easeOutQuad",e37,i,b],["eid258",d,4000,0,"easeOutQuad",e37,b,i],["eid259",d,4040,0,"easeOutQuad",e37,i,b],["eid260",d,6000,0,"easeOutQuad",e37,b,i],["eid240",o,0,750,"easeOutQuad",e37,'0','0.7'],["eid245",o,1000,1000,"easeOutQuad",e37,'0.7','0'],["eid247",o,2000,750,"easeOutQuad",e37,'0.000000','0.7'],["eid249",o,3000,1000,"easeOutQuad",e37,'0.7','0'],["eid251",o,4000,750,"easeOutQuad",e37,'0.000000','0.7'],["eid253",o,5000,1000,"easeOutQuad",e37,'0.7','0'],["eid27",lf,0,1000,"easeOutQuad",e38,'0px','723px'],["eid35",lf,1000,1000,"easeOutQuad",e38,'723px','-140px']]}},"Pensione":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{t:g,id:x39,r:[x40,x40,x33,x34,x22,x22],f:[x4,im+g41,x40,x40]},{t:x,r:[x42,x43,x44,x45,x22,x22],ts:['','','','',i],id:x46,text:s10,align:rt,n:[x12,[24,p],x47,x13,i,l,x14,'']},{t:x,r:[x48,x49,x50,x45,x22,x22],ts:['','','','',i],id:x51,text:s10,align:rt,n:[x12,[24,p],x52,x13,i,l,x14,'']},{r:[x20,x53,x54,x55,x22,x22],overflow:x56,id:x57,t:g,f:[x4,im+g58,x40,x40]},{r:[x20,x53,x54,x55,x22,x22],overflow:x56,id:x59,t:g,f:[x4,im+g60,x40,x40]},{t:x,align:po,text:s61,r:[x62,x63,x64,x65,x22,x22],id:x66,n:[x67,[28,p],x17,x68,i,l,x14,''],o:x25,ts:['','','','',i]},{r:[x69,x70,x71,x72,x22,x22],t:m,s:[0,x24,i],id:x73,o:x25,cu:x74,f:[x16]}],style:{'${symbolSelector}':{r:[_,_,x33,x34]}}},tt:{d:1000,a:y,data:[["eid141",lf,250,500,"easeOutQuad",e75,'870px','645px'],["eid74",o,750,250,"easeOutQuad",e76,'0','1'],["eid138",lf,250,500,"easeOutQuad",e77,'870px','645px']]}},"Unici":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{r:[x40,x40,x33,x34,x22,x22],id:x78,t:g,f:[x4,im+g79,x40,x40]},{n:[x12,[24,p],x52,x13,i,l,x14,''],t:x,align:rt,id:x80,text:s10,ts:['','','','',i],r:[x81,x82,x83,x45,x22,x22]},{n:[x12,[24,p],x84,x13,i,l,x14,''],t:x,align:rt,id:x85,text:s10,ts:['','','','',i],r:[x86,x82,x87,x45,x22,x22]},{n:[x12,[24,p],x47,x13,i,l,x14,''],t:x,align:rt,id:x88,text:s10,ts:['','','','',i],r:[x86,x89,x87,x45,x22,x22]},{n:[x12,[24,p],x47,x13,i,l,x14,''],t:x,align:po,id:x90,text:s10,ts:['','','','',i],r:[x70,x89,x86,x45,x22,x22]},{n:[x12,[24,p],x52,x13,i,l,x14,''],t:x,align:rt,id:x91,text:s10,ts:['','','','',i],r:[x92,x89,x87,x45,x22,x22]},{t:m,id:x93,s:[0,x24,i],r:[x94,x95,x96,x97,x22,x22],f:[x98]},{t:g,id:x99,r:[x100,x101,x102,x103,x22,x22],cl:x104,f:[x4,im+g105,x40,x40]},{t:g,overflow:x56,id:x106,r:[x20,x107,x54,x55,x22,x22],f:[x4,im+g58,x40,x40]},{t:g,overflow:x56,id:x108,r:[x20,x107,x54,x55,x22,x22],f:[x4,im+g60,x40,x40]},{t:x,align:po,ts:['','','','',i],n:[x67,[28,p],x17,x68,i,l,x14,''],text:s109,r:[x110,x111,x112,x113,x22,x22],o:x25,id:x114},{t:m,r:[x115,x70,x116,x117,x22,x22],o:x25,id:x118,s:[0,x24,i],cu:x74,f:[x16]}],style:{'${symbolSelector}':{r:[_,_,x33,x34]}}},tt:{d:1250,a:y,data:[["eid97",o,1000,250,"easeOutQuad",e119,'0','1'],["eid98",lf,250,750,"easeOutQuad",e120,'870px','645px'],["eid100",lf,250,750,"easeOutQuad",e121,'870px','645px']]}},"tcm":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{r:[x40,x40,x33,x34,x22,x22],id:x122,t:g,f:[x4,im+g123,x40,x40]},{n:[x12,[24,''],x47,x13,i,'',x14,''],t:x,id:x124,text:s10,align:rt,r:[x125,x126,x127,x45,x22,x22]},{n:[x12,[24,p],x52,x13,i,l,x14,''],t:x,align:rt,id:x128,ts:['','','','',i],text:s10,r:[x129,x126,x127,x45,x22,x22]},{t:m,id:x130,s:[0,x24,i],r:[x131,x132,x96,x97,x22,x22],f:[x133]},{t:g,overflow:x56,id:x134,r:[x20,x53,x54,x55,x22,x22],f:[x4,im+g58,x40,x40]},{t:g,overflow:x56,id:x135,r:[x20,x53,x54,x55,x22,x22],f:[x4,im+g60,x40,x40]},{t:x,id:x136,ts:['','','','',i],n:[x67,[28,p],x17,x68,i,l,x14,''],r:[x137,x138,x139,x140,x22,x22],text:s109,o:x25,align:po},{t:m,r:[x141,x142,x143,x144,x22,x22],o:x25,id:x145,s:[0,x24,i],cu:x74,f:[x16]},{n:[x12,[24,p],x17,x68,i,l,x14,''],t:x,align:po,id:x146,ts:['','','','',i],text:s147,r:[x142,x126,x148,x138,x22,x22]},{r:[x100,x149,x102,x103,x22,x22],id:x150,t:g,f:[x4,im+g105,x40,x40]}],style:{'${symbolSelector}':{r:[_,_,x33,x34]}}},tt:{d:1250,a:y,data:[["eid118",lf,250,750,"easeOutQuad",e151,'870px','645px'],["eid119",lf,250,750,"easeOutQuad",e152,'870px','645px'],["eid117",o,1000,250,"easeOutQuad",e153,'0','1']]}},"polizze_TCM":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[],style:{'${symbolSelector}':{r:[_,_,x102,x103]}}},tt:{d:0,a:y,data:[]}},"clicca":{v:x1,mv:x2,b:x3,stf:i,cg:i,rI:n,cn:{dom:[{t:g,id:x154,o:x155,r:[x156,x157,x158,x159,x22,x22],f:[x4,im+g160,x40,x40]}],style:{'${symbolSelector}':{r:[_,_,x158,x159]}}},tt:{d:6000,a:y,data:[["eid194",o,0,150,"linear",e161,'0.000000','1'],["eid196",o,150,150,"linear",e161,'1','0'],["eid198",o,300,200,"linear",e161,'0.000000','1'],["eid203",o,1000,125,"linear",e161,'1','0'],["eid224",o,1200,0,"linear",e161,'0','0.000000'],["eid225",o,1350,0,"linear",e161,'0.000000','1'],["eid226",o,1500,200,"linear",e161,'0.000000','1'],["eid227",o,2200,125,"linear",e161,'1','0'],["eid234",o,2405,150,"linear",e161,'0.000000','1'],["eid235",o,2555,150,"linear",e161,'1','0'],["eid236",o,2705,200,"linear",e161,'0.000000','1'],["eid237",o,3405,125,"linear",e161,'1','0'],["eid271",lf,0,0,"linear",e161,'-43px','-43px'],["eid218",lf,1125,75,"linear",e161,'-43px','175px'],["eid232",lf,2325,80,"linear",e161,'175px','443px'],["eid272",tp,0,0,"linear",e161,'-9px','-9px'],["eid219",tp,1125,75,"linear",e161,'-9px','30px'],["eid233",tp,2325,80,"linear",e161,'30px','-23px']]}}};AdobeEdge.registerCompositionDefn(compId,symbols,fonts,scripts,resources,opts);})("EDGE-234");
(function($,Edge,compId){var Composition=Edge.Composition,Symbol=Edge.Symbol;Edge.registerEventBinding(compId,function($){
//Edge symbol: 'stage'
(function(symbolName){Symbol.bindElementAction(compId,symbolName,"${pulsante1}","click",function(sym,e){sym.getSymbol("Stage").getSymbol("Tabelle").play(1)});
//Edge binding end
Symbol.bindElementAction(compId,symbolName,"${pulsante2}","click",function(sym,e){sym.getSymbol("Stage").getSymbol("Tabelle").play(2001)});
//Edge binding end
Symbol.bindElementAction(compId,symbolName,"${pulsante3}","click",function(sym,e){sym.getSymbol("Stage").getSymbol("Tabelle").play(4001)});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",0,function(sym,e){sym.getSymbol("Tabelle").getSymbol("Pensione").$("importoErogFondoPensione_TXT").html(importoErogFondoPensione);sym.getSymbol("Tabelle").getSymbol("Pensione").$("pezziProgOpen_TXT").html(pezziProgOpen);if(pezziProgOpen==0)frasePensione='Datti da fare, basta veramente poco per ottenere molto!';if(pezziProgOpen>0&&pezziProgOpen<=3)frasePensione='E’ un buon inizio, ma puoi fare molto di più!';if(pezziProgOpen>3)frasePensione='Complimenti, stai andando fortissimo!';sym.getSymbol("Tabelle").getSymbol("Pensione").$("messaggio_pensione").html(frasePensione);var ritaglio_Unici=34*pezziTCM;var slide_Unici=importoErogPremiUniciAnteBonus*1.6;if(slide_Unici>480)slide_Unici=480;sym.getSymbol("Tabelle").getSymbol("Unici").$('slide_Unici').css('width',slide_Unici);sym.getSymbol("Tabelle").getSymbol("Unici").$('pallini').css('clip','rect(0px, '+ritaglio_Unici+'px, 20px, 0px)');sym.getSymbol("Tabelle").getSymbol("Unici").$("pezziUnici_TXT").html(pezziUnici);sym.getSymbol("Tabelle").getSymbol("Unici").$("premiUnici_TXT").html(premiUnici_TXT);sym.getSymbol("Tabelle").getSymbol("Unici").$("percBonusPU_TXT").html(percBonusPU+'%');sym.getSymbol("Tabelle").getSymbol("Unici").$("importoErogPremiUnici_TXT").html(importoErogPremiUnici_TXT);sym.getSymbol("Tabelle").getSymbol("Unici").$("importoErogPremiUniciAnteBonus2_TXT").html(importoErogPremiUniciAnteBonus_TXT);sym.getSymbol("Tabelle").getSymbol("Unici").$("importoErogPremiUniciAnteBonus_TXT").html(importoErogPremiUniciAnteBonus_TXT);if(importoErogPremiUniciAnteBonus<200)fraseUnici='Datti da fare, la soglia è ancora lontana!';if(importoErogPremiUniciAnteBonus>=200)fraseUnici='Ci sei quasi, mancano solo '+(300-importoErogPremiUniciAnteBonus).toFixed(2)+' euro alla soglia minima!';if(importoErogPremiUniciAnteBonus>=300&&pezziTCM<5)fraseUnici='Soglia raggiunta! E con '+(5-pezziTCM)+' polizze TCM puoi avere anche il bonus!';if(importoErogPremiUniciAnteBonus>=300&&pezziTCM>=5&&pezziTCM<10)fraseUnici='Ti sei aggiudicato anche il bonus! Con altre '+(10-pezziTCM)+' polizze TCM puoi raddoppiarlo!';if(importoErogPremiUniciAnteBonus>=300&&pezziTCM>9)fraseUnici='Complimenti, hai ottenuto il bonus più alto!';sym.getSymbol("Tabelle").getSymbol("Unici").$("messaggio_unici").html(fraseUnici);var ritaglio_TCM=34*pezziTCM;if(pezziTCM>5)ritaglio_TCM=170;premiTCM<=1000?slide_tcm=premiTCM*0.24:slide_tcm=240+(premiTCM-1000)*0.14;if(slide_tcm>480)slide_tcm=480;sym.getSymbol("Tabelle").getSymbol("tcm").$('pallini_TCM').css('clip','rect(0px, '+ritaglio_TCM+'px, 20px, 0px)');sym.getSymbol("Tabelle").getSymbol("tcm").$('slide_TCM').css('width',slide_tcm);sym.getSymbol("Tabelle").getSymbol("tcm").$("premiTCM_TXT").html(premiTCM_TXT);sym.getSymbol("Tabelle").getSymbol("tcm").$("importoErogTcm_TXT").html(importoErogTcm_TXT);sym.getSymbol("Tabelle").getSymbol("tcm").$("percTCM").html(percTCM+'%');if(pezziTCM<5)fraseTCM='Ti mancano ancora '+(5-pezziTCM)+' polizze per ottenere l’incentivo!';if(pezziTCM>=5&&premiTCM<=1000)fraseTCM='Hai sbloccato l’incentivo, ora punta all’aliquota successiva!';if(pezziTCM>=5&&premiTCM>1000&&premiTCM<3000)fraseTCM='Hai sbloccato l’incentivo, ora punta all’aliquota massima!';if(pezziTCM>=5&&premiTCM>=3000)fraseTCM='Incentivo raggiunto con aliquota massima: stai andando alla grande!';sym.getSymbol("Tabelle").getSymbol("tcm").$("messaggio_tcm").html(fraseTCM);sym.$("importoErogPremiUnici_HM").html(importoErogPremiUnici_TXT);sym.$("importoErogFondoPensione_HM").html(importoErogFondoPensione);sym.$("importoErogTcm_HM").html(importoErogTcm_TXT);sym.$("importoErogTotale_HM").html(importoErogTOTALE);});
//Edge binding end
})("stage");
//Edge symbol end:'stage'

//=========================================================

//Edge symbol: 'Tabelle'
(function(symbolName){Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",1000,function(sym,e){sym.stop();sym.getSymbol("Pensione").play();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",2000,function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").getSymbol("Pensione").stop(0);sym.stop();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",3000,function(sym,e){sym.stop();sym.getSymbol("Unici").play();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",4000,function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").getSymbol("Unici").stop(0);sym.stop();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",5000,function(sym,e){sym.stop();sym.getSymbol("tcm").play();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",6000,function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").getSymbol("tcm").stop(0);sym.stop();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",0,function(sym,e){sym.stop();});
//Edge binding end
})("Tabelle");
//Edge symbol end:'Tabelle'

//=========================================================

//Edge symbol: 'Pensione'
(function(symbolName){Symbol.bindElementAction(compId,symbolName,"${tabella_pensione}","click",function(sym,e){});
//Edge binding end
Symbol.bindElementAction(compId,symbolName,"${btn_pensione}","click",function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").play(1001);});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",0,function(sym,e){sym.stop();sym.$("pappagallo_messaggi_positivi").hide();sym.$("pappagallo_messaggi_negativi").hide();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",250,function(sym,e){if(pezziProgOpen>0){sym.$("pappagallo_messaggi_positivi").show();sym.$("messaggio_pensione").show().css('color','#213a52');}else{sym.$("pappagallo_messaggi_negativi").show();}});
//Edge binding end
})("Pensione");
//Edge symbol end:'Pensione'

//=========================================================

//Edge symbol: 'Unici'
(function(symbolName){Symbol.bindElementAction(compId,symbolName,"${chiudi_unici}","click",function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").play(3001);});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",0,function(sym,e){sym.stop();sym.$("pappagallo_messaggi_positivi_Unici").hide();sym.$("pappagallo_messaggi_negativi_Unici").hide();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",250,function(sym,e){if(importoErogPremiUniciAnteBonus>=300){sym.$("pappagallo_messaggi_positivi_Unici").show();sym.$("messaggio_unici").show().css('color','#213a52');}else{sym.$("pappagallo_messaggi_negativi_Unici").show();}});
//Edge binding end
})("Unici");
//Edge symbol end:'Unici'

//=========================================================

//Edge symbol: 'tcm'
(function(symbolName){Symbol.bindElementAction(compId,symbolName,"${chiudi_tcm}","click",function(sym,e){sym.getComposition().getStage().getSymbol("Tabelle").play(5001);});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",0,function(sym,e){sym.stop();sym.$("pappagallo_messaggi_positivi_TCM").hide();sym.$("pappagallo_messaggi_negativi_TCM").hide();});
//Edge binding end
Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",250,function(sym,e){if(pezziTCM>=5){sym.$("pappagallo_messaggi_positivi_TCM").show();sym.$("messaggio_tcm").show().css('color','#213a52');}else{sym.$("pappagallo_messaggi_negativi_TCM").show();}});
//Edge binding end
})("tcm");
//Edge symbol end:'tcm'

//=========================================================

//Edge symbol: 'polizze_TCM'
(function(symbolName){})("polizze_TCM");
//Edge symbol end:'polizze_TCM'

//=========================================================

//Edge symbol: 'clicca'
(function(symbolName){Symbol.bindTriggerAction(compId,symbolName,"Default Timeline",6000,function(sym,e){sym.play(0);});
//Edge binding end
})("clicca");
//Edge symbol end:'clicca'
})})(AdobeEdge.$,AdobeEdge,"EDGE-234");