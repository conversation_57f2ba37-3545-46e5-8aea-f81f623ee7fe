(function(ng, module) {

	module.config(['$stateProvider', function($stateProvider) {
		$stateProvider
			.state('apps',			{ url: '/apps/',			templateUrl: 'apps/apps.html',		controller: 'app.apps.Ctrl',		navbar: 'apps' })
			.state('apps-iframe',	{ url: '/apps/iframe/:id/',	templateUrl: 'apps/iframe.html',	controller: 'app.apps.IframeCtrl',	navbar: 'apps' })
		;
	}]);

	// initialization
	var apps = {};
	module.run(['$rootScope', '$http', function($rootScope) {
		$rootScope.$on('xng.auth:init', function(ev, authData) {

			let currentYear = 2024;

			if(authData) {
				// Agit
				if(/^KA|AGENTE$/.test(authData.UTYPE)) apps.Agit = { url: '/_/#!/agit/', name: 'Agit',  isAgendo3: true };
				// ContAct
				// 2017-02-21 HIDE // if(/^KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR|AGENTE$/.test(authData.UTYPE)) apps.ContAct = { url: 'contact/', iframe: null, name: 'ContAct' };
				if(/^KA|AMMINISTRATORE|DISTRICTMGR$/.test(authData.UTYPE)) apps.Accordo2 = { url: '/_/#!/accordo-economico/2025', isAgendo3: true, name: 'Accordo Economico' };
				// GestioneContributi
				if(/^AMMINISTRATORE$/.test(authData.UTYPE) && ((parseInt(1+authData['ACL']['CP'],2) & parseInt(10001000000000,2)) === parseInt(10001000000000,2))) apps.GestContributi = { url: 'contributi/', iframe: null, name: 'Gestione Contributi' };
				if(/^KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR$/.test(authData.UTYPE)) apps.GestContributi = { url: '/_/#!/contributi/' + new Date().getFullYear(), isAgendo3: true, name: 'Gestione Contributi' };
				// Reportistica
				if(authData.ACL.apps.reportistica) apps.Reportistica = { url: 'reportistica/', iframe: null, name: 'Reportistica' };
				// Santino
				if(/^KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR|AGENTE$/.test(authData.UTYPE)) apps.Santino = { url: 'santino/', iframe: null, name: 'Santino' };
				// TassoZero
				if(/^KA|AMMINISTRATORE$/.test(authData.UTYPE)) apps.TassoZero = { url: 'tassozero/', iframe: null, name: 'Tasso Zero' };
				// WelcomeBack
				if(/^KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR|AGENTE$/.test(authData.UTYPE)) apps.WelcomeBack = { url: 'welcomeback/', iframe: null, name: 'Welcome Back' };
                // PianiAgenzia2
                if(/^KA|AMMINISTRATORE|DIREZ|AREAMGR|DISTRICTMGR$/.test(authData.UTYPE)) apps.PianiAgenzia2 = { url: 'pianiagenzia2/', iframe: null, name: 'Percorsi di Crescita' };
				// AGENTI
				if(/^AGENTE$/.test(authData.UTYPE)) {
                    apps.Anagrafica = { url: 'anagrafica/', iframe: null, name: 'Anagrafica collaboratori' };
					apps.Planning = { url: 'Planning', iframe: '/Agente/Planning/', name: 'Planning' };
                }
				// IVASS
				if(/^KA|FORMAZIONE|AUDIT/.test(authData.UTYPE) || authData.ACL.apps.ivass) {
					apps.Ivass = { url: 'ivass/start/', iframe: null, name: 'Ivass' };
				}
				// Avanzamenti
				if(/^KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR/.test(authData.UTYPE)) apps.Avanzamenti = { url: '/_/#!/avanzamenti/' + currentYear, iframe: null, name: 'Avanzamenti Rappel', isAgendo3: true };
				else if(authData.ACL.apps.avanzamenti) apps.Avanzamenti = {url: '/_/#!/avanzamenti/' + currentYear, iframe: null, name: 'Avanzamenti Rappel', isAgendo3: true };

				// Avanzamenti: link agente
				if(/^AGENTE$/.test(authData.UTYPE)) apps.Avanzamenti = { url: '/_/#!/avanzamenti/' + currentYear, iframe: null, name: 'Avanzamenti Rappel', isAgendo3: true };

				if(/^KA|FORMAZIONE|AGENTE|AREAMGR|DISTRICTMGR|ASV$/.test(authData.UTYPE)) {
					apps.Neo = { url: '/_/#!/formazione-neo', iframe: null, name: 'Formazione NEO', isAgendo3: true };
				}
                if(/^KA|FORMAZIONE|AREAMGR|DISTRICTMGR$/.test(authData.UTYPE)) {
                    apps.Webinar = { url: 'webinar/', iframe: null, name: 'Webinar' };
                }
			} else {
				apps = {};
			}
		});
	}]);

	// Ctrl
	module.controller('app.apps.Ctrl', ['$scope', function($scope) {
		$scope.apps = apps;
	}]);

	module.controller('app.apps.IframeCtrl', ['$scope', '$stateParams', function($scope, $stateParams) {
		$scope.name = apps[$stateParams.id].name;
		$scope.iframe = apps[$stateParams.id].iframe;
	}]);

})(angular, angular.module('app.apps', []));
