<script setup>

import {useRoute} from 'vue-router'
import {ArrowDownOnSquareStackIcon} from '@heroicons/vue/16/solid';
import InCorsoIcon from './icons/InCorsoIcon.vue';
import InElaborazioneIcon from './icons/DatiInElaborazioneIcon.vue';
import TerminataIcon from './icons/TerminataIcon.vue';
import LiquidataIcon from './icons/LiquidataIcon.vue';
import {PhCar, PhCube, PhSmiley} from "@phosphor-icons/vue";
import {ref} from "vue";
import {useUserStore} from "@/_common/stores/user.js";

const props = defineProps({
  incentive: {
    type: Object,
    required: true
  }
});

const route = useRoute()
const year = route.params.year
const authData = ref(useUserStore());

const parseProgramName = (programName) => programName?.replaceAll(" ", "-");

/**
 * Genera l'URL per l'incentivazione basato sul programma e, per gli anni dal 2025 in poi,
 * differenzia il percorso tra frontend e backend in base al tipo utente
 *
 * @param {Object} incentive - Oggetto contenente i dettagli dell'incentivazione
 * @returns {string} URL completo dell'incentivazione
 */
const getIncentiveUrl = (incentive) => {
  // Caso speciale per la focus 2025 che ha il frontend fatto in Vue e il backend fatto in AngularJS
  if (incentive.id === 332 && !['AGENTE', 'INTERMEDIARIO'].includes(authData.value.UTYPE)) {
    return `/${parseProgramName(incentive.program)}/`;
  }

  // Per le incentivazioni dal 2025 in poi, aggiunge /incentive prima del programma
  if (year >= 2025) {
    // Costruisce il path con /incentive prima del nome del programma
    return `/_/#!/incentive/${parseProgramName(incentive.program)}`;
  }

  // Per gli anni precedenti al 2025, mantiene il path originale
  return `/${parseProgramName(incentive.program)}/`;
};

const getImgUrl = (incentive) => `https://${window.staticHost}/themes/incentivazioni/media/${parseProgramName(incentive.program)}/logo-inc.svg`;
const getDettaglioUrl = (incentive) => `/api/apps/${parseProgramName(incentive.program)}/excel-detail`;

const formatDate = (dateString) => {
  const date = new Date(dateString).toLocaleDateString('it-IT');
  if (date === 'Invalid Date') {
    return "";
  }
  return date;
}

/* Period example
year	"2024"
program	"fullbenessere 2024"
name	"fullbenessere 2024"
status	"closed"
protection	"0"
start	"2024-06-01"
end	"2024-10-31"
lastUpdate	"2024-09-18 16:13:33"
lastUpdateLabel	"2024-09-17"
*/

function getLatestPeriod(periods) {
  if (!periods || periods.length === 0) {
    return null;
  }
  periods.sort((a, b) => new Date(b.start) - new Date(a.start));
  return periods[0];
}

function getCurrentPeriodStatus(incentive) {
  const lastPeriod = getLatestPeriod(incentive.periods);
  return lastPeriod?.status;
}

function showTutelaIcon(incentive, value) {
  return incentive.protection && (incentive.protection & value)

}

</script>

<template>
  <div class="card w-full h-full min-h-[460px] px-5 py-3">
    <div class="mb-10 flex justify-between">
      <div>
        <InCorsoIcon v-if="['active', 'started'].includes(getCurrentPeriodStatus(incentive))" />
        <InElaborazioneIcon v-if="getCurrentPeriodStatus(incentive) === 'processing'" />
        <TerminataIcon v-if="getCurrentPeriodStatus(incentive) === 'closed'" />
        <LiquidataIcon v-if="getCurrentPeriodStatus(incentive) === 'liquidated'" />
      </div>
      <div class="space-x-1">
          <PhCar v-if="showTutelaIcon(incentive, 1)" size="20" weight="fill" color="#8F8F8F" class="inline" />
          <PhSmiley v-if="showTutelaIcon(incentive, 2)" size="20" weight="bold" color="#8F8F8F" class="inline" />
          <PhCube v-if="showTutelaIcon(incentive, 3)" size="20" weight="bold" color="#8F8F8F" class="inline" />
      </div>
    </div>
    <a :href="getIncentiveUrl(incentive)" class="flex-grow flex justify-center items-center mb-10">
      <img class="h-[150px] " :src="getImgUrl(incentive)" :alt="`img-${incentive.id}`" />
    </a>
    <div class="divide-y divide-gray-200 divide-opacity-75 px-5 text-gray-600">
      <div class="py-3 text-xl">
        <p class="font-bold">{{ incentive.program?.toUpperCase() }}</p>
      </div>
      <div class="py-3 text-sm">
        <p>Periodo: {{ formatDate(incentive.start) }} - {{ formatDate(incentive.end) }}</p>
      </div>
      <div class="py-3 text-sm">
        <p>Aggiornamento: {{ formatDate(incentive.lastUpdate) }} </p>
      </div>
      <div class="py-3 text-sm" v-if="['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB'].includes(authData.UTYPE) && incentive.hasStatusDownload">
        <a class="btn service" :href="getDettaglioUrl(incentive)">
          <ArrowDownOnSquareStackIcon class="w-5 inline" />
          Scarica Dettaglio
        </a>
      </div>
    </div>
  </div>
</template>
