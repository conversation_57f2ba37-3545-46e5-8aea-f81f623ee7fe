<script setup>
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, PhDotsThreeOutlineVertical, PhDownloadSimple, PhMicrosoftExcelLogo, PhWrench} from "@phosphor-icons/vue";
import {Menu, MenuButton, MenuItem, MenuItems} from "@headlessui/vue";
import MaterialUploadModal from "@/apps/formazione-backend/classrooms/MaterialUploadModal.vue";
import {ref} from "vue";

const materialUploadModal = ref(null)
const props = defineProps(['classroomId'])

function openMaterialUploadModal(fileType) {
    materialUploadModal.value.openMaterialUploadModal(fileType)
}
</script>

<template>

    <MaterialUploadModal ref="materialUploadModal" />

    <Menu as="div" class="relative inline-block text-left">
        <div>
            <MenuButton
                class="btn service btn-icon"
            >
                <PhWrench size="20" /> Gestione materiali <PhCaretDown size="18" />
            </MenuButton>
        </div>

        <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
        >
            <MenuItems
                class="absolute right-0 mt-2 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none z-10"
            >
                <MenuItem as="button"
                          class="block p-2 hover:bg-blue-700 hover:text-white w-full rounded-md cursor-pointer text-left"
                          @click="openMaterialUploadModal('PARTICIPANTS')">
                    Carica foglio presenze compilato
                </MenuItem>
                <MenuItem as="button"
                          class="block p-2 hover:bg-blue-700 hover:text-white w-full rounded-md cursor-pointer text-left"
                          @click="openMaterialUploadModal('MEMO')">
                    Carica verbale compilato
                </MenuItem>
            </MenuItems>
        </transition>
    </Menu>
</template>