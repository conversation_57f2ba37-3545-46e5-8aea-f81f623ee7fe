<script setup>

    import { reactive, ref, unref, watch, onMounted } from 'vue';
    import { useRoute, useRouter } from "vue-router";
    import { PhFileXls, PhFilePdf } from "@phosphor-icons/vue";
    import { avanzamentiApi } from '@/apps/avanzamenti/api/avanzamenti.js';
    import { Table } from '@/libs/table.js';
    import Pagination from "@/_common/components/Table/Pagination.vue";
    import Sorter from "@/_common/components/Table/Sorter.vue";
    import { PhWarningCircle, PhMagnifyingGlass, PhWarning } from "@phosphor-icons/vue";
    import { Disclosure, DisclosurePanel } from '@headlessui/vue';
    import { formatDate, formatPrice } from '@/libs/formatter';
    import { useUserStore } from "@/_common/stores/user.js";
    import Chart from "primevue/chart";

    const authData = useUserStore();
    const route = useRoute();
    const router = useRouter();
    const selectedYear = ref(route.params.year);

    let selectedType = ref(selectType(route));
    let currentRoute = route.name;

    function selectType(route)
    {
        switch (route.name) {
            case 'advancements-rp':
                return 'RP';
            case 'advancements-vt':
                return 'V';
        }
    }

    watch(route, async (r) => {
        if (r.name === currentRoute) {
            let newYear = r?.params?.year;
            if (!newYear || newYear == unref(selectedYear)) {
                return;
            }
            selectedYear.value = newYear;
        } else {
            currentRoute = r.name;
            selectedType.value = selectType(r);
        }
        await init();
    });

    const selectedAgencyId = ref(route.params.agencyId);
    const months = [0, 'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
    const agencyData = await avanzamentiApi.getAgencyDetail(selectedAgencyId.value);

    let indexedData = ref([]);
    let orderedData = ref([]);
    let availableMonths = ref([]);
    let isItThereAnyData = ref(false);
    let hasObjectives = ref(false);

    const detailPolicyColumns = [
        {
            key: 'year',
            label: 'Anno',
        },
        {
            key: 'agenzia_id',
            label: 'Cod. Agenzia',
        },
        {
            key: 'areaIncentivazione',
            label: 'Area',
        },
        {
            key: 'policyNumber',
            label: 'Numero Polizza',
        },
        {
            key: 'dtdeco',
            label: 'dtdeco',
        },
        {
            key: 'dtemis',
            label: 'dtemis',
        },
        {
            key: 'dtgc',
            label: 'dtgc',
        },
        {
            key: 'aliqObj',
            label: 'aliqObj',
        },
        {
            key: 'descrizione',
            label: 'descrizione',
        },
        {
            key: 'famiglia',
            label: 'famiglia',
        },
        {
            key: 'famigliaDescr',
            label: 'famigliaDescr',
        },
        {
            key: 'flg',
            label: 'flg',
        },
        {
            key: 'flgEsito',
            label: 'flgEsito',
        },
        {
            key: 'npComp',
            label: 'npComp',
        },
        {
            key: 'premperfez',
            label: 'premperfez',
        },
        {
            key: 'ramo',
            label: 'ramo',
        },
        {
            key: 'tar',
            label: 'tar',
        },
        {
            key: 'versagg',
            label: 'versagg',
        }
    ];
    const filtered = {
        policyNumber: 'text'
    };

    const tableDetailPolicy = reactive(new Table(avanzamentiApi.getAvanzamentoPolicyList, {
        pageSize: 10
    }));

    async function init()
    {
        if (!agencyData) {
            return window.location.href = '/apps/';
        }
        if (authData.UTYPE === 'AREAMGR' && agencyData.area !== authData.AREA) {
            return router.push({ name: 'advancements-list', params: { year: unref(selectedYear) }});
        }
        if (authData.UTYPE === 'DISTRICTMGR' && (agencyData.area !== authData.AREA || agencyData.district !== authData.DISTRICT)) {
            return router.push({ name: 'advancements-list', params: { year: unref(selectedYear) }});
        }
        if (authData.UTYPE === 'AGENTE' && (agencyData.id !== authData.AGENZIA || agencyData.area !== authData.AREA || agencyData.district !== authData.DISTRICT)) {
            return router.push({ name: 'advancements-rp', params: { year: unref(selectedYear), agencyId: authData.AGENZIA }});
        }
        const data = await avanzamentiApi.getAvanzamentoAgency(selectedYear.value, selectedAgencyId.value);
        await setTableData(data);

        if (selectedType.value === 'V') {
            tableDetailPolicy.filters = {
                year: {operation: 'EQ', value: selectedYear.value},
                agenzia_id: {operation: 'EQ', value: selectedAgencyId.value},
                areaIncentivazione: {operation: 'EQ', value: 'TCM'},
            };
            await tableDetailPolicy.fetchData();
        }
    }

    async function setTableData(data) {
        let indexed = [];
        let ordered = [];
        data.forEach((item) => {
            indexed[item.level] = indexed[item.level] || {};
            indexed[item.level][item.type] = indexed[item.level][item.type] || [];
            indexed[item.level][item.type].push(item);

            ordered[item.month] = ordered[item.month] || {};
            ordered[item.month][item.level] = ordered[item.month][item.level] || {};
            ordered[item.month][item.level][item.type] = item;
        });

        let months = Object.keys(ordered).map(function (value) {
            return parseInt(value)
        });

        for (let i = months[months.length - 1] + 1; i <= 12; i++) {
            months.push(i);
        }

        indexedData.value = indexed;
        orderedData.value = ordered;
        availableMonths.value = months;
        isItThereAnyData.value = !!indexed[1];
        hasObjectives.value = Boolean(indexed && indexed[1] && indexed[1]['RP'][0].objectiveYear > 0);

        updateChart(indexed, ordered, months);
    }

    // Chart
    let chartIsReady = ref(false);
    function updateChart(indexedData, orderedData, aMonths)
    {
        if (indexedData.length === 0) {
            return null;
        }
        makeChart();

        // Lines for objectives level1 and level2.
        let lineRP1 = [], lineRP2 = [];

        aMonths.forEach((month) => {
            if (!orderedData[month]) {
                return;
            }

            lineRP1.push(orderedData[month]?.[1]?.[selectedType.value]?.objectiveMonthCumulative ?? 0);
            lineRP2.push((orderedData[month]?.[2]?.[selectedType.value]?.objectiveMonthCumulative ?? 0) - (orderedData[month]?.[1]?.[selectedType.value]?.objectiveMonthCumulative ?? 0));
        });

        chartData.value.datasets[0].data = lineRP1;
        chartData.value.datasets[1].data = lineRP2;

        let barRP0;
        let barRP1;
        if (indexedData[1]) {
            barRP0 = indexedData[1][selectedType.value].map(function(value, index) {
                if (!hasObjectives.value) {
                    return value.consumptiveMonth;
                }

                if (value.consumptiveMonth <= lineRP1[index]) {
                    return value.consumptiveMonth;
                }

                return lineRP1[index];
            });

            chartData.value.datasets[2].data = barRP0;

            barRP1 = indexedData[1][selectedType.value].map(function(value, index) {
                if (value.consumptiveMonth <= lineRP1[index] + lineRP2[index]) {
                    return value.consumptiveMonth - barRP0[index];
                }

                return lineRP1[index] + lineRP2[index] - barRP0[index];
            });
        }
        if (hasObjectives.value) {
            chartData.value.datasets[3].data = barRP1;

            let barRP2;
            if (indexedData[2]) {
                barRP2 = indexedData[2][selectedType.value].map(function(value, index) {
                    if (value.consumptiveMonth > lineRP2[index]) {
                        return value.consumptiveMonth - barRP0[index] - barRP1[index];
                    }

                    return 0;
                })
            }

            chartData.value.datasets[4].data = barRP2;
        }

        chartData.value.labels = [];

        aMonths.forEach((value) => {
            chartData.value.labels.push(months[value]);
        });

        chartIsReady.value = true;
    }

    let chartData = ref({});
    let chartOpts = ref({});
    function makeChart() {
        chartData.value = {
            datasets: [

                // Linea livello 1
                {
                    label: '1° livello di obiettivo',
                    data: [],
                    backgroundColor: '#4EBDEA',
                    borderColor: '#4EBDEA',
                    fill: false,
                    lineTension: 0,
                    // Changes this dataset to become a line
                    type: 'line'
                },

                // Linea livello 2
                {
                    label: '2° livello di obiettivo',
                    data: [],
                    backgroundColor: '#FF8500',
                    borderColor: '#FF8500',
                    fill: false,
                    lineTension: 0,
                    // Changes this dataset to become a line
                    type: 'line'
                },

                // Barra Avanzamento "verde"
                {
                    label: selectedType.value === 'V' ? 'Nuova produzione' : 'Incassi',
                    data: [],
                    backgroundColor: '#C7D300'
                },

                // Set every bar section to green after COVID update

                // Barra Avanzamento "Livello 1"
                {
                    hidden: true,
                    data: [],
                    //backgroundColor: '#4EBDEA'
                    backgroundColor: '#C7D300'
                },

                // Barra Avanzamento "Livello 2"
                {
                    hidden: true,
                    data: [],
                    //backgroundColor: '#008DC6'
                    backgroundColor: '#C7D300'
                }
            ],
            labels: []
        };

        chartOpts.value = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    ticks: {
                        beginAtZero: true
                    },
                    title: {
                        display: true,
                        text: 'Mesi'
                    },
                    barPercentage: 0.1,
                    maxBarThickness: 5
                },
                y: {
                    stacked: true,
                    ticks: {
                        callback: function(value) {
                            if (parseInt(value) >= 1000){
                                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                            } else {
                                return value;
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'Incasso in €'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    labels: {
                        filter: function(legendItem, chart) {
                            return !chart.datasets[legendItem.datasetIndex].hidden;
                        }
                    }
                },
                tooltip: {
                    mode: 'nearest',
                    callbacks: {
                        label: function(tooltipItem) {
                            const data = tooltipItem.chart.data;
                            const index = tooltipItem.dataIndex;
                            const datasetIndex = tooltipItem.datasetIndex;

                            function format(...values) {
                                let value = values.reduce((a, b) => a + b, 0);
                                return '€ ' + value.toFixed(0).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
                            }

                            switch (datasetIndex) {
                                case 1:
                                    return format(
                                        data.datasets[0].data[index],
                                        data.datasets[1].data[index]
                                    );
                                case 3:
                                    return format(
                                        data.datasets[2].data[index],
                                        data.datasets[3].data[index]
                                    );
                                case 4:
                                    return format(
                                        data.datasets[2].data[index],
                                        data.datasets[3].data[index],
                                        data.datasets[4].data[index]
                                    );
                                default:
                                    return format(data.datasets[datasetIndex].data[index]);
                            }
                        }
                    }
                }
            },
            hover: {
                mode: 'nearest',
                intersect: true
            }
        };
    }

    await init();
</script>

<template>
    <div class="container relative">
        <div class="w-full card shadow-xl">
            <div class="card-header no-border">
                <div class="title title-sm" style="font-weight: inherit">
                    <div v-if="selectedType === 'RP'">{{ selectedAgencyId + ' ' + agencyData?.nome }} - Rami Preferiti</div>
                    <div v-else-if="selectedType === 'V'">{{ selectedAgencyId + ' ' + agencyData?.nome }} - Vita TCM</div>
                    <div class="card-header-subtitle">Avanzamenti - dati mensili</div>
                </div>

                <div class="toolbar">
                    <a :href="`/api/apps/avanzamenti/pdf`"
                       class="btn primary">
                        <PhFilePdf class="size-5" />
                        Scarica il piano
                    </a>
                </div>
            </div>
            <div class="card-body body-fit">
                <div v-if="isItThereAnyData">
                    <div class="overflow-x-auto">
                        <Chart
                                v-if="chartIsReady && indexedData.length > 0"
                                type="bar"
                                :data="chartData"
                                :options="chartOpts"
                                class="h-[30rem] m-5"
                        />
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Mese</th>
                                <th></th>
                                <th class="yellow-bottom-border" v-show="selectedType === 'RP'">Incassi</th>
                                <th class="yellow-bottom-border" v-show="selectedType === 'V'">Nuova Produzione</th>
                                <th class="yellow-bottom-border">Delta Obj Annuo (€)</th>
                                <th class="yellow-bottom-border">VS Obj Annuo (%)</th>
                                <th></th>
                                <th class="blue-bottom-border">Delta Obj 1° liv (€)</th>
                                <th class="blue-bottom-border">VS Obj 1° liv (%)</th>
                                <th></th>
                                <th class="orange-bottom-border">Delta Obj 2° liv (€)</th>
                                <th class="orange-bottom-border">VS Obj 2° liv (%)</th>
                                <th></th>
                                <th>Stima Rappel (€)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="month in availableMonths">
                                <td align="left">{{ months[month] }}</td>
                                <td></td>
                                <td align="center">
                                    <span v-if="orderedData[month]"><strong>{{ formatPrice(orderedData[month][1][selectedType]?.consumptiveMonth) }}</strong></span>
                                    <span v-else> - </span>
                                </td>
                                <td align="center">
                                    <span v-if="orderedData[month]">{{ formatPrice(orderedData[month][1][selectedType]?.consumptiveMonthDeltaYear) }}</span>
                                    <span v-else> - </span>
                                </td>
                                <td align="center">
                                    <span v-if="orderedData[month]">{{ orderedData[month][1][selectedType]?.consumptiveMonthIncreaseYear.toFixed(2) }}%</span>
                                    <span v-else> - </span>
                                </td>
                                <td></td>
                                <td align="center">
                                    <span v-if="orderedData[month]"><strong>{{ formatPrice(orderedData[month][1][selectedType]?.consumptiveMonthDeltaMonth) }}</strong></span>
                                    <span v-else> - </span>
                                </td>
                                <td align="center">
                                    <span v-if="orderedData[month]">{{ orderedData[month][1][selectedType]?.consumptiveMonthIncreaseMonth.toFixed(2) }}%</span>
                                    <span v-else> - </span>
                                </td>
                                <td></td>
                                <td align="center">
                                    <span v-if="orderedData[month]"><strong>{{ formatPrice(orderedData[month][2][selectedType]?.consumptiveMonthDeltaMonth) }}</strong></span>
                                    <span v-else> - </span>
                                </td>
                                <td align="center">
                                    <span v-if="orderedData[month]">{{ orderedData[month][2][selectedType]?.consumptiveMonthIncreaseMonth.toFixed(2) }}%</span>
                                    <span v-else> - </span>
                                </td>
                                <td></td>
                                <td align="center">
                                    <span v-if="orderedData[month]"><strong>{{ formatPrice(orderedData[month][1][selectedType]?.rappel) }}</strong></span>
                                    <span v-else> - </span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="m-2">
                        <Disclosure>
                            <DisclosureButton class="px-2 py-6 bg-gray-100 w-full text-left rounded flex disclosure">
                                <div><strong><PhWarningCircle :size="35" class="inline mr-2 mr-5 ph-w-circle" /></strong></div>
                                <div>
                                    <strong>Ai fini della statistica di avanzamento dati mensili vengono considerati:</strong>
                                    <ul v-show="selectedType === 'RP'">
                                        <li>- gli incassi dei contratti Rami Preferiti con effetto e titolo anno d'incentivazione in corso (gennaio-dicembre)</li>
                                        <li>- gli incassi al lordo delle "Gare Pubbliche"</li>
                                        <li>- gli incassi al lordo delle esclusioni di contratti per i quali tra Direzione e Agenzia sia stata convenuta l'esclusione dall'incentivazione</li>
                                    </ul>
                                    <ul v-show="selectedType === 'V'">
                                        <li>- i premi dei contratti Tcm con effetto anno d'incentivazione in corso (gennaio-dicembre)</li>
                                        <li>- i premi al lordo delle esclusioni di contratti per i quali tra Direzione e Agenzia sia stata convenuta l'esclusione dell'incentivazione</li>
                                    </ul>
                                </div>
                            </DisclosureButton>
                        </Disclosure>
                    </div>

                    <div v-if="selectedType === 'V'">
                        <div class="card-header mt-7" style="border-top: 1px solid #e4e6ec">
                            <div class="title title-sm" style="font-weight: inherit">
                                <div>Detteglio Polizze</div>
                                <div class="card-header-subtitle">Riferito all'ultimo mese di avanzamento obiettivo</div>
                            </div>

                            <div class="toolbar" style="position: relative">
                                <div class="input-icons">
                                    <input class="filter-policy" placeholder="Inserisci numero polizza"
                                           v-if="filtered?.['policyNumber']" :type="filtered['policyNumber']"
                                           :value="tableDetailPolicy?.filters['policyNumber']?.value" @input="e => tableDetailPolicy.applyFilters({
                                        ['policyNumber']: {
                                            operation: 'LIKE',
                                            value: e.target.value,
                                        }
                                    })" />
                                    <PhMagnifyingGlass :size="20" class="inline" />
                                </div>

                                <a :href="`/api/apps/avanzamenti/excel/${unref(selectedAgencyId)}/${unref(selectedYear)}/TCM`"
                                   class="btn primary">
                                    <PhFileXls class="size-5" />
                                    Scarica Excel
                                </a>
                            </div>
                        </div>
                        <div class="card-body body-fit">
                            <div v-if="tableDetailPolicy?.rows && tableDetailPolicy?.rows?.length > 0">
                                <div class="overflow-x-auto">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th v-for="column in detailPolicyColumns" :key="column.key">
                                                <span v-if="column.key !== 'year' && column.key !== 'agenzia_id' && column.key !== 'areaIncentivazione'">
                                                    <span class="cursor-pointer" @click="() => tableDetailPolicy.toggleSort(column.key)">
                                                        {{ column.label }}
                                                        <Sorter :ordering="tableDetailPolicy?.sorters[column.key]" />
                                                    </span>
                                                </span>
                                                <span v-else>{{ column.label }}</span>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="row in tableDetailPolicy?.rows" :key="row.id">
                                            <td>{{ row.year }}</td>
                                            <td>{{ row.agenzia_id }}</td>
                                            <td>{{ row.areaIncentivazione }}</td>
                                            <td>{{ row.policyNumber }}</td>
                                            <td>{{ row.dtdeco ? formatDate(row.dtdeco) : '' }}</td>
                                            <td>{{ row.dtemis ? formatDate(row.dtemis) : '' }}</td>
                                            <td>{{ row.dtgc ? formatDate(row.dtgc) : '' }}</td>
                                            <td>{{ row.aliqObj }}</td>
                                            <td>{{ row.descrizione }}</td>
                                            <td>{{ row.famiglia }}</td>
                                            <td>{{ row.famigliaDescr }}</td>
                                            <td>{{ row.flg }}</td>
                                            <td>{{ row.flgEsito }}</td>
                                            <td>{{ row.npComp }}</td>
                                            <td>{{ row.premperfez }}</td>
                                            <td>{{ row.ramo }}</td>
                                            <td>{{ row.tar }}</td>
                                            <td>{{ row.versagg }}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="flex justify-center items-center gap-x-2 py-5">
                                    <Pagination :table="tableDetailPolicy" />
                                </div>
                            </div>
                            <div v-else class="text-center m-5">
                                <span>
                                    <PhWarning :size="35" class="inline mr-2 ph-w-circle" />
                                    Non ci sono polizze da mostrare per l'anno {{ selectedYear }}.
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-center m-5">
                    <span>
                        <PhWarning :size="35" class="inline mr-2 ph-w-circle" />
                        Non ci sono dati da mostrare per l'anno {{ selectedYear }}.
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
    .card-header-subtitle { font-size: 1rem }
    .disclosure { background-color: #EDF5FF; cursor: initial; font-size: 0.875rem }
    .ph-w-circle { color: #005A9C }

    th.yellow-bottom-border,
    th.blue-bottom-border,
    th.orange-bottom-border { border-bottom: 3px solid; text-align: center }

    th.yellow-bottom-border { border-bottom-color: #C9D343 }
    th.blue-bottom-border { border-bottom-color: #63A1C3 }
    th.orange-bottom-border { border-bottom-color: #EF8B34 }

    .input-icons svg { position: absolute; left: 167px; top: 7px }
    input.filter-policy::placeholder { font-size: 0.8rem; font-style: italic }
    input.filter-policy { border: solid 1px #e4e6ec; padding: 0 5px; height: 35px }
</style>
