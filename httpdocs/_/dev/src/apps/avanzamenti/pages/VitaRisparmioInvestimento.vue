<script setup>

    import { reactive, ref, unref, watch } from 'vue';
    import { useRoute, useRouter } from "vue-router";
    import { PhFileXls, PhFilePdf } from "@phosphor-icons/vue";
    import { avanzamentiApi } from '@/apps/avanzamenti/api/avanzamenti.js';
    import { Table } from '@/libs/table.js';
    import Pagination from "@/_common/components/Table/Pagination.vue";
    import Sorter from "@/_common/components/Table/Sorter.vue";
    import { PhWarningCircle, PhMagnifyingGlass, PhWarning, PhInfo } from "@phosphor-icons/vue";
    import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
    import { formatDate, formatPrice, formatInteger } from '@/libs/formatter';
    import { useUserStore } from "@/_common/stores/user.js";

    const authData = useUserStore();
    const route = useRoute();
    const router = useRouter();
    const selectedYear = ref(route.params.year);

    watch(route, async (r) => {
        let newYear = r?.params?.year;
        if (!newYear || newYear == unref(selectedYear)) {
            return;
        }
        selectedYear.value = newYear;
        await init();
    });

    const selectedAgencyId = ref(route.params.agencyId);
    const months = [0, 'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
    const agencyData = await avanzamentiApi.getAgencyDetail(selectedAgencyId.value);

    const detailPolicyColumns = [
        {
            key: 'year',
            label: 'Anno',
        },
        {
            key: 'agenzia_id',
            label: 'Cod. Agenzia',
        },
        {
            key: 'areaIncentivazione',
            label: 'Area',
        },
        {
            key: 'policyNumber',
            label: 'Numero Polizza',
        },
        {
            key: 'dtdeco',
            label: 'dtdeco',
        },
        {
            key: 'dtemis',
            label: 'dtemis',
        },
        {
            key: 'dtgc',
            label: 'dtgc',
        },
        {
            key: 'aliqObj',
            label: 'aliqObj',
        },
        {
            key: 'descrizione',
            label: 'descrizione',
        },
        {
            key: 'famiglia',
            label: 'famiglia',
        },
        {
            key: 'famigliaDescr',
            label: 'famigliaDescr',
        },
        {
            key: 'flg',
            label: 'flg',
        },
        {
            key: 'flgEsito',
            label: 'flgEsito',
        },
        {
            key: 'npComp',
            label: 'npComp',
        },
        {
            key: 'premperfez',
            label: 'premperfez',
        },
        {
            key: 'ramo',
            label: 'ramo',
        },
        {
            key: 'tar',
            label: 'tar',
        },
        {
            key: 'versagg',
            label: 'versagg',
        }
    ];
    const filtered = {
        policyNumber: 'text'
    };

    const tableDetailPolicy = reactive(new Table(avanzamentiApi.getAvanzamentoPolicyList, {
        pageSize: 10
    }));


    let showTooltip = ref(false);
    let tableData = ref([]);
    let isItThereAnyData = ref(false);

    async function setTableData(data)
    {
        let tempData = [];
        data.forEach((item) => {
            // Mail 15/04/2020 14:14
            if (parseInt(item.year) === 2019 && parseInt(item.month) === 9) {
                return;
            }
            tempData.push(item);
        });

        tableData.value = tempData;
        isItThereAnyData.value = !!tempData[1];
    }

    async function init()
    {
        if (!agencyData) {
            return window.location.href = '/apps/';
        }
        if (authData.UTYPE === 'AREAMGR' && agencyData.area !== authData.AREA) {
            return router.push({ name: 'advancements-list', params: { year: unref(selectedYear) }});
        }
        if (authData.UTYPE === 'DISTRICTMGR' && (agencyData.area !== authData.AREA || agencyData.district !== authData.DISTRICT)) {
            return router.push({ name: 'advancements-list', params: { year: unref(selectedYear) }});
        }
        if (authData.UTYPE === 'AGENTE' && (agencyData.id !== authData.AGENZIA || agencyData.area !== authData.AREA || agencyData.district !== authData.DISTRICT)) {
            return router.push({ name: 'advancements-vri', params: { year: unref(selectedYear), agencyId: authData.AGENZIA }});
        }
        const data = await avanzamentiApi.getAvanzamento(selectedYear.value, selectedAgencyId.value);
        await setTableData(data);

        tableDetailPolicy.filters = {
            year: { operation: 'EQ', value: selectedYear.value },
            agenzia_id: { operation: 'EQ', value: selectedAgencyId.value },
            areaIncentivazione: { operation: 'EQ', value: 'RIP' },
        };
        await tableDetailPolicy.fetchData();
    }

    await init();

</script>

<template>
    <div class="container relative">
        <div class="w-full card shadow-xl">
            <div class="card-header no-border">
                <div class="title title-sm" style="font-weight: inherit">
                    <div>{{ selectedAgencyId + ' ' + agencyData?.nome }} - Vita Risparmio e Investimenti</div>
                </div>

                <div class="toolbar">
                    <a :href="`/api/apps/avanzamenti/pdf`"
                       class="btn primary">
                        <PhFilePdf class="size-5" />
                        Scarica il piano
                    </a>
                </div>
            </div>
            <div v-if="isItThereAnyData">
                <div class="card-body body-fit">
                    <div class="overflow-x-auto">
                        <table class="table first-table">
                            <thead>
                            <tr>
                                <th>Mese</th>
                                <th></th>
                                <th>Numero Pol Limite</th>
                                <th></th>
                                <th>NP Annuale</th>
                                <th>NP Unico</th>
                                <th></th>
                                <th>NP Totale</th>
                                <th></th>
                                <th>Stima Rappel (€)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="row in tableData">
                                <td>{{ months[row.month] }}</td>
                                <td></td>
                                <td align="center"><span>{{ formatInteger(row.vriNpLimit) }}</span></td>
                                <td></td>
                                <td align="center"><span>{{ formatInteger(row.vriNpYear) }}</span></td>
                                <td align="center"><span>{{ formatInteger(row.vriNpUnique) }}</span></td>
                                <td></td>
                                <td align="center"><span>{{ formatInteger(row.vriNpTotal) }}</span></td>
                                <td></td>
                                <td align="center"><strong>{{ formatPrice(row.vriRappel) }}</strong></td>
                            </tr>
                            </tbody>
                        </table>

                        <div class="m-2">
                            <Disclosure>
                                <DisclosureButton class="px-2 py-6 bg-gray-100 w-full text-left rounded flex disclosure">
                                    <div><strong><PhWarningCircle :size="35" class="inline mr-2 mr-5 ph-w-circle" /></strong></div>
                                    <div>
                                        <div>
                                            <strong>Ai fini della statistica di avanzamento dati mensili vengono considerati:</strong>
                                            <ul>
                                                <li>- i premi dei contratti con effetto anno d'incentivazione in corso (gennaio-dicembre)</li>
                                                <li>- i premi al lordo delle polizze "fuori Target"</li>
                                                <li>- i premi al lordo delle esclusioni di contratti per i quali tra Direzione e Agenzia sia stata convenuta l'esclusione dell'incentivazione</li>
                                                <li>- i valori dei "Rappel Stimati al lordo del limite erogabile in funzione della numerositò di polizze</li>
                                            </ul>
                                        </div>
                                        <div class="mt-5" style="position: relative">
                                            <div v-if="showTooltip" class="c-tooltip">
                                                <table>
                                                    <thead>
                                                    <tr><th>Numero polizze</th><th>Minimo erogabile</th></tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr><td>Fino a 1500</td><td>€ 500</td></tr>
                                                    <tr><td>da 1501 a 2500</td><td>€ 800</td></tr>
                                                    <tr><td>da 2501 a 4500</td><td>€ 1200</td></tr>
                                                    <tr><td>oltre 4500</td><td>€ 1500</td></tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <strong>Numero polizze limite
                                                <PhInfo
                                                        :size="20"
                                                        class="inline ph-info"
                                                        @mouseenter="showTooltip = true"
                                                        @mouseleave="showTooltip = false"
                                                />
                                            </strong>
                                            <div>Viene stabilito un limite di rappel erogabile in funzione della numerosità di polizze attive Danni/Vita alla data del 31/12 dell'anno precedente a quello di riferimento (con esclusione delle polizze Grandine, Tutela Giudiziaria e Assistenza, dei Rami Credito, Cauzioni, Trasporti, Aeronautici).</div>
                                        </div>
                                    </div>
                                </DisclosureButton>
                            </Disclosure>
                        </div>
                    </div>
                </div>
                <div class="card-header mt-7" style="border-top: 1px solid #e4e6ec">
                    <div class="title title-sm" style="font-weight: inherit">
                        <div>Detteglio Polizze</div>
                        <div class="card-header-subtitle">Riferito all'ultimo mese di avanzamento obiettivo</div>
                    </div>

                    <div class="toolbar" style="position: relative">
                        <div class="input-icons">
                            <input class="filter-policy" placeholder="Inserisci numero polizza"
                                    v-if="filtered?.['policyNumber']" :type="filtered['policyNumber']"
                                    :value="tableDetailPolicy?.filters['policyNumber']?.value" @input="e => tableDetailPolicy.applyFilters({
                                ['policyNumber']: {
                                    operation: 'LIKE',
                                    value: e.target.value,
                                }
                            })" />
                            <PhMagnifyingGlass :size="20" class="inline" />
                        </div>

                        <a :href="`/api/apps/avanzamenti/excel/${unref(selectedAgencyId)}/${unref(selectedYear)}/RIP`"
                           class="btn primary">
                            <PhFileXls class="size-5" />
                            Scarica Excel
                        </a>
                    </div>
                </div>
                <div class="card-body body-fit">
                    <div v-if="tableDetailPolicy.rows.length > 0">
                        <div class="overflow-x-auto">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th v-for="column in detailPolicyColumns" :key="column.key">
                                        <span v-if="column.key !== 'year' && column.key !== 'agenzia_id' && column.key !== 'areaIncentivazione'">
                                            <span class="cursor-pointer" @click="() => tableDetailPolicy.toggleSort(column.key)">
                                                {{ column.label }}
                                                <Sorter :ordering="tableDetailPolicy?.sorters[column.key]" />
                                            </span>
                                        </span>
                                        <span v-else>{{ column.label }}</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row in tableDetailPolicy?.rows" :key="row.id">
                                <td>{{ row.year }}</td>
                                <td>{{ row.agenzia_id }}</td>
                                <td>{{ row.areaIncentivazione }}</td>
                                <td>{{ row.policyNumber }}</td>
                                <td>{{ row.dtdeco ? formatDate(row.dtdeco) : '' }}</td>
                                <td>{{ row.dtemis ? formatDate(row.dtemis) : '' }}</td>
                                <td>{{ row.dtgc ? formatDate(row.dtgc) : '' }}</td>
                                <td>{{ row.aliqObj }}</td>
                                <td>{{ row.descrizione }}</td>
                                <td>{{ row.famiglia }}</td>
                                <td>{{ row.famigliaDescr }}</td>
                                <td>{{ row.flg }}</td>
                                <td>{{ row.flgEsito }}</td>
                                <td>{{ row.npComp }}</td>
                                <td>{{ row.premperfez }}</td>
                                <td>{{ row.ramo }}</td>
                                <td>{{ row.tar }}</td>
                                <td>{{ row.versagg }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="flex justify-center items-center gap-x-2 py-5">
                            <Pagination :table="tableDetailPolicy" />
                        </div>
                    </div>
                    <div v-else class="text-center m-5">
                        <span>
                            <PhWarning :size="35" class="inline mr-2 ph-w-circle" />
                            Non ci sono polizze da mostrare per l'anno {{ selectedYear }}.
                        </span>
                    </div>
                </div>
            </div>
            <div v-else class="text-center m-5">
                <span>
                    <PhWarning :size="35" class="inline mr-2 ph-w-circle" />
                    Non ci sono dati da mostrare  per l'anno {{ selectedYear }}.
                </span>
            </div>
        </div>
    </div>
</template>

<style scoped>
    .first-table th:not(:first-child) { text-align: center }
    .input-icons svg { position: absolute; left: 167px; top: 7px }
    input.filter-policy::placeholder { font-size: 0.8rem; font-style: italic }
    input.filter-policy { border: solid 1px #e4e6ec; padding: 0 5px; height: 35px }
    .disclosure, .card-header-subtitle { font-size: 0.875rem }
    .disclosure { background-color: #EDF5FF; cursor: initial }
    .ph-w-circle, .ph-info { color: #005A9C }
    .ph-info { cursor: pointer; }
    .c-tooltip {
        background-color: white;
        position: absolute;
        top: -130px;
        left: 155px;
        border-radius: 8px;
    }
    .c-tooltip td, .c-tooltip th {
        padding: 5px 10px;
        font-size: 12px;
    }
</style>
