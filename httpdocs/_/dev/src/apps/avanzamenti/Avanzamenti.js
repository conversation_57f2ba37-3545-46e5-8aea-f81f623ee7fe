import Avanzamenti from "@/apps/avanzamenti/Avanzamenti.vue";
import List from "@/apps/avanzamenti/pages/List.vue"; 
import Data from "@/apps/avanzamenti/pages/Data.vue";
import VitaRisparmioInvestimento from "@/apps/avanzamenti/pages/VitaRisparmioInvestimento.vue";
import {useUserStore} from "@/_common/stores/user.js";

export default [
    {
        beforeEnter: (to, from) => {
            const authData = useUserStore();
            if ( ![ 'KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE' ].includes(authData.UTYPE) ) {
                window.location.href = '../';
            }
        },
        path: '/avanzamenti/:year',
        name: 'advancements',
        component: Avanzamenti,
        redirect: {
            name: 'advancements-list'
        },
        meta: {
            breadcrumb: 'Avanzamenti'
        },
        children: [
            {
                path: 'elenco',
                name: 'advancements-list',
                component: List
            },
            {
                path: ':agencyId/rami-preferiti',
                name: 'advancements-rp',
                component: Data
            },
            {
                path: ':agencyId/vita-tcm',
                name: 'advancements-vt',
                component: Data
            },
            {
                path: ':agencyId/vita-risparmio-investimento',
                name: 'advancements-vri',
                component: VitaRisparmioInvestimento
            }
        ]
    }
];

