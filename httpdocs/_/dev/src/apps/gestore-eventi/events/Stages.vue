<script setup>
import {reactive, ref} from "vue";
import {Table} from "@/libs/table.js";
import Sorter from "@/_common/components/Table/Sorter.vue";
import Pagination from "@/_common/components/Table/Pagination.vue";
import {stages} from "@/apps/gestore-eventi/api/stages.js";
import {formatDate} from "@/libs/formatter.js";
import StageToggleButton from "@/apps/gestore-eventi/stages/StageToggleButton.vue";
import { PhFloppyDiskBack, PhPencil} from "@phosphor-icons/vue";
import {toast} from "vue3-toastify";
import FillingWidget from "@/_common/components/FillingWidget.vue";
import {useUserStore} from "@/_common/stores/user.js";

const props = defineProps(['eventId'])
const authData = useUserStore();
const AUTHORIZED = ['KA', 'FORMAZIONE', 'AMMINISTRATORE']

const columns = [
    {
        key: 'year',
        label: 'Anno',
    },
    {
        key: 'day',
        label: 'Data',
        formatter: formatDate,
    },
    {
        key: 'name',
        label: 'Luogo',
    },
    {
        key: 'active',
        label: 'Status',
    },
    {
        key: 'seats',
        label: 'Posti',
        hidden: ['DISTRICTMGR']
    },
    {
        key: 'enrolled',
        label: 'Iscr. da portale',
    },
    {
        key: 'extra',
        label: 'Iscr. direzionale',
    },
    {
        key: 'enrolledTotal',
        label: 'Iscr. TOT',
    },
];

const stagesTable = reactive(new Table((opt) => stages.getAllByEventIdTable(opt, props.eventId), {pageSize: 20}));
await stagesTable.fetchData();

let showEditArray = ref({
    extra: [],
    seats: [],
})

async function handleUpdatedStatus() {
    await stagesTable.fetchData();
}

function showEdit(type, index) {
    showEditArray.value[type][index] = true
}

async function saveExtraEdit(index, stageId, extraSeats) {
    const res = await stages.updateStageExtra(stageId, extraSeats)
    if (!res.success) {
        toast.error('Errore imprevisto durante il salvataggio.')
        return
    }
    toast.success('Tappa aggiornata.')
    showEditArray.value.extra[index] = false
}

async function saveSeatsEdit(index, stageId, seats) {
    const res = await stages.updateStageSeats(stageId, seats)
    if (!res.success) {
        toast.error('Errore imprevisto durante il salvataggio.')
        return
    }
    toast.success('Tappa aggiornata.')
    showEditArray.value.seats[index] = false
}

function calcFillPercentage(enrolledTotal, seats) {
    return ((enrolledTotal/seats)*100).toFixed()
}

function filteredColumns(columns) {
    return columns.filter(item => !item.hidden || !item.hidden.includes(authData.UTYPE) )
}
</script>

<template>
    <div class="card-body body-fit">
        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                <tr>
                    <th v-if="AUTHORIZED.includes(authData.UTYPE)"></th>
                    <th class="th-label cursor-pointer"
                        @click="() => stagesTable.toggleSort(column.key)"
                        v-for="column in filteredColumns(columns)" :key="column.key"
                        :class="{'text-right' : ['seats', 'extra', 'enrolled', 'enrolledTotal'].includes(column.key)}">
                        {{ column.label }}
                        <Sorter :ordering="stagesTable.sorters[column.key]"/>
                    </th>
                    <th class="text-right" v-if="AUTHORIZED.includes(authData.UTYPE)">% occupata</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(row, index) in stagesTable.rows" :key="row.id">
                    <td v-if="AUTHORIZED.includes(authData.UTYPE)">
                        <FillingWidget :fill-percentage="calcFillPercentage(row['enrolledTotal'], row['seats'])" />
                    </td>
                    <td v-for="column in filteredColumns(columns)" :key="column.key" :class="{'text-right' : ['seats', 'extra', 'enrolled', 'enrolledTotal'].includes(column.key)}">
                        <template v-if="column.key === 'day'">{{ stagesTable.printCell(row, column) }}</template>
                        <template v-else-if="column.key === 'active'">
                            <StageToggleButton :stage="row" @updated-status="handleUpdatedStatus"/>
                        </template>
                        <template v-else-if="column.key === 'name'">
                            <div class="highlight cursor-pointer" @click="$emit('stageSelected', row['id'])">{{ row['city'] }} - {{ row['name'] }}</div>
                        </template>
                        <div class="flex items-center justify-end gap-x-1" v-else-if="column.key === 'seats' && ! column.hidden.includes(authData.UTYPE) ">
                            <span v-if="!showEditArray.seats[index]">{{ row[column.key] }}</span>
                            <input
                                class="border-2 rounded border-black"
                                v-model="row.seats"
                                v-if="showEditArray.seats[index]"
                                @keyup.enter="saveSeatsEdit(index, row.id, row.seats)"
                                type="text" style="display: inline; text-align: right; width: 35px">
                            <button v-tooltip="'Modifica'" v-if=" !showEditArray.seats[index] && AUTHORIZED.includes(authData.UTYPE) " type="button" class="btn compact btn-icon service" @click="showEdit('seats', index)"><PhPencil size="18" /></button>
                            <button v-tooltip="'Salva'" v-if="showEditArray.seats[index]" type="button" class="btn compact btn-icon success" @click="saveSeatsEdit(index, row.id, row.seats)"><PhFloppyDiskBack size="18" weight="fill" /></button>
                        </div>
                        <div class="flex items-center justify-end gap-x-1" v-else-if="column.key === 'extra'">
                            <span v-if="!showEditArray.extra[index]">{{ row[column.key] }}</span>
                            <input
                                class="border-2 rounded border-black"
                                v-model="row.extra"
                                v-if="showEditArray.extra[index]"
                                @keyup.enter="saveExtraEdit(index, row.id, row.extra)"
                                type="text" style="display: inline; text-align: right; width: 25px">
                            <button v-tooltip="'Modifica'" v-if=" !showEditArray.extra[index] && AUTHORIZED.includes(authData.UTYPE) " type="button" class="btn compact btn-icon service" @click="showEdit('extra', index)"><PhPencil size="18" /></button>
                            <button v-tooltip="'Salva'" v-if="showEditArray.extra[index]" type="button" class="btn compact btn-icon success" @click="saveExtraEdit(index, row.id, row.extra)"><PhFloppyDiskBack size="18" weight="fill" /></button>
                        </div>
                        <template v-else>{{ row[column.key] }}</template>
                    </td>
                    <td class="text-right" v-if="AUTHORIZED.includes(authData.UTYPE)">
                        <span v-if="row['seats'] > 0">{{ ((row['enrolledTotal']/row['seats'])*100).toFixed() }}%</span>
                        <span v-if="row['seats'] === 0">ND</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="flex justify-center items-center gap-x-2 py-4 border-t" v-if="stagesTable && stagesTable.pageQnt > 1">
        <Pagination :table="stagesTable" />
    </div>
</template>

<style scoped>
.text-right {
    width: 150px;
}
.highlight {
    font-weight: 700;
    color: #0b3cb8;
}
.highlight:hover {
    color: #072c88;
    text-decoration: underline;
}
</style>