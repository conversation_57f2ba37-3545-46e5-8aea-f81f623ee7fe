<script setup>
import { ref, reactive, unref, watch, defineEmits } from 'vue';
import { ErrorMessage, Field, Form, configure, defineRule, useSetFieldError, useForm } from "vee-validate";
import { AutoComplete, Textarea, RadioButton, RadioButtonGroup } from "primevue";
import { PhX, PhFloppyDisk } from '@phosphor-icons/vue';
import { useRoute, useRouter } from "vue-router";
import { contributiApi } from '@/apps/contributi/api/contributi.js';
import { formatPrice } from '@/libs/formatter';
import { useUserStore } from "@/_common/stores/user.js";
import { toast } from "vue3-toastify";
import { uploadFiles, allowDrop, dropHandler, deactivateUploadFeedback, onDropUpload, onChangeUpload, uploadFileChunks } from '@/libs/upload';
import { computed } from 'vue';
import { toRaw } from 'vue';



// TODO: Use form instead of pages

const authData = useUserStore();
const route = useRoute();
const router = useRouter();
const selectedYear = ref(route.params.year);
const emit = defineEmits(['jollySubmit', 'jollyAbort']);

let UI = null;
switch (authData?.UTYPE) {
    case 'KA':
    case 'AMMINISTRATORE':
        UI = 'DIREZIONE';
        break;
    case 'AREAMGR':
        UI = 'AREAMGR';
        break;
    case 'DISTRICTMGR':
        UI = 'DISTRICTMGR';
        break;
}


const form = ref(null);

const props = defineProps({
    type: {
        type: String,
        default: 'JOLLY'
    },
    contributo: {
        type: Object,
        default: {},
    }
});

let agencySuggestions = ref([])
const upload = ref(null);
let contributo = reactive(typeof props.contributo === 'object' ? { ...props.contributo } : {});
let type = ref(props.type)
let agencySelected = ref(null);
let agencyId = computed(() => type === 'EDIT' ? props.contributo.agenzia_id : agencySelected.value?.id);

const suggestAgencies = async () => {
    const res = await contributiApi.queryAgencies(
        {
            filters: {
                query: {
                    operation: 'EQ',
                    value: agencySelected.value
                },
                status: {
                    operation: '!EQ',
                    value: 'OFF'
                }
            },
            sorters: {
                id: 'ASC'
            }
        });
    if (!res?.success) {
        toast.error('Errore imprevisto durante il recupero delle agenzie');
        return;
    }
    agencySuggestions.value = res.data;
}

const onSubmit = async (values, actions) => {

    let statusVal = props.contributo?.status;
    if(status.value !== null ){
        statusVal = status.value;
    }

    values = {
        ...contributo.value,
        ...values,
        status: statusVal,
    }
    let res = null;
    if (type.value === 'EDIT') {
        res = await contributiApi.update(values, contributo.value?.id);
    } else {
        res = await contributiApi.create(values)
    }
    const areValidationErrors = !!(Object.keys(res?.errors ?? {})?.length > 0);
    if (!res?.success && !areValidationErrors) {
        toast.error('Errore imprevisto durante il salvataggio');
        return;
    }
    if (areValidationErrors) {
        form.value.setErrors(parseValidationErrors(res.errors));
        return;
    }
    if (type.value === 'ADD' || type.value === 'JOLLY') {
        contributo.value = { ...contributo.value, ...res.data };
    }
    if (type.value === 'EDIT') {
        contributo.value = { ...contributo.value, ...values };
    }
    type.value = 'EDIT';
    if (hasAttachment(toRaw(unref(contributo)))) {
        let attachRes = await uploadAttachment(toRaw(unref(contributo)));
        if (attachRes?.length) {
            attachRes = attachRes[0];
            return;
        }
        contributo.value.attachment = attachRes?.data;
        if (!attachRes?.success) {
            toast.error("Errore imprevisto durante il salvataggio dell'allegato");
            return;
        }
    }
    toast.success('Contributo salvato con successo');
    emit('jollySubmit', contributo.value);
}

const abort = () => {
    router.push('elenco')
    emit('jollyAbort', false);
}

const hasAttachment = (data) => {
    const files = upload.value.files;
    const url = upload.value.getAttribute('url');
    if (!files?.length || !data?.id) {
        return false;
    }
    return true;
};

const uploadAttachment = async (data) => {
    if (!hasAttachment(data)) {
        return;
    }
    const res = await uploadFileChunks(files[0], url, data);
    return res;
}

const parseValidationErrors = (errors) => {
    const parsedErrors = {};
    for (let [key, value] of Object.entries(errors)) {
        if (key === 'importo' && value === 'BUDGET_ESAURITO') {
            value = "Budget ESAURITO o NON SUFFICIENTE";
        }
        parsedErrors[key] = value;
    }
    return parsedErrors;
}

const onBudgedChange = (e) => {
    contributo.budget = e.target.value;
}


const editStatus = (newStatus) => {
    status.value = newStatus;
}
let status = ref(null);

const defaultBudget = props.type === 'JOLLY' ? 'DIREZ' : null;

</script>

<template>
    <div class="container">
        <Form @submit="onSubmit" :initial-values="contributo" ref="form" v-slot="{ errors, submitForm }">
            <div class="w-full card shadow-xl">
                <div class="card-header no-border">
                    <div class="title title-sm" style="font-weight: inherit">
                        <h2 v-if="type === 'ADD'">Nuovo contributo</h2>
                        <h2 v-if="type === 'EDIT'">Modifica contributo</h2>
                        <h2 v-if="type === 'JOLLY'">Nuovo contributo JOLLY</h2>
                    </div>
                </div>

                <div class="card-body grid-cols-1 md:grid-cols-3 gap-5 p-5">
                    <div class="form-group">
                        <label for="agenzia_id">Agenzia</label>
                        <AutoComplete v-if="type !== 'EDIT'" v-model="agencySelected" :suggestions="agencySuggestions"
                            :option-label="(item) => `${item.id} - ${item.nome}`" @complete="suggestAgencies"
                            :class="{ 'has-error rounded': errors.title }"
                            pt:list-container="bg-white border shadow-md rounded-md overflow-y-scroll mt-2"
                            pt:option="px-2 py-2 border-b hover:bg-blue-600 hover:text-white cursor-pointer"
                            pt:empty-message="hidden" pt:loader="hidden" />
                        <Field :type="type === 'EDIT' ? 'text' : 'hidden'" v-model="agencyId" name="agenzia_id"
                            :readonly="type === 'EDIT'" rules="required" />
                        <div class="form-error">
                            <ErrorMessage name="agenzia_id" />
                        </div>
                    </div>

                    <div class="form-group md:col-span-2">
                        <label for="titolo">Iniziative</label>
                        <Field type="text" name="titolo" rules="required" :class="{ 'has-error': errors.titolo }" />
                        <div class="form-error">
                            <ErrorMessage name="titolo" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="importo">Importo</label>
                        <Field type="number" size="8" step="1" name="importo" rules="required"
                            :class="{ 'has-error': errors.importo }" />
                        <div class="form-error">
                            <ErrorMessage name="importo" />
                        </div>

                    </div>

                    <div class="form-group">
                        <label for="rateizzato">Metodo</label>
                        <div class="flex flex-wrap gap-4 my-2">
                            <Field type="radio" name="rateizzato" :value="true" /> Mensile
                            <Field type="radio" name="rateizzato" :value="false" /> Unica soluzione
                        </div>
                        <div class="form-error">
                            <ErrorMessage name="rateizzato" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="budget">Budget</label>
                        <div lass="flex flex-wrap gap-4 my-2" rules="required" :default-value="defaultBudget" required>
                            <template v-if="UI === 'AREAMGR'">
                                <Field type="radio" name="budget" value="AREA" rules="required"
                                    @change="onBudgedChange" /> dalla disponibilità
                                di Area
                            </template>

                            <template v-if="UI == 'DISTRICTMGR'">
                                <Field type="radio" name="budget" value="DISTRICT" rules="required"
                                    @change="onBudgedChange" /> dalla
                                disponibilità di District
                            </template>

                            <Field type="radio" name="budget" value="DIREZ" rules="required" @change="onBudgedChange" />
                            dalla disponibilità di
                            Direzione
                            <small v-if="type != 'JOLLY'">(soggetto ad approvazione)</small>
                        </div>
                        <div class="form-error">
                            <ErrorMessage name="budget" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="codice">Tipologia contributo</label>
                        <Field as="select" name="codice" :class="{ 'has-error': errors.tipo }" rules="required">
                            <option value="D210">PRODUTTIVO (cod. D210)</option>
                            <option value="V210">PRODUTTIVO (cod. V210)</option>
                            <option value="C210">PRODUTTIVO (cod. C210)</option>
                        </Field>
                        <div class="form-error">
                            <ErrorMessage name="codice" />
                        </div>

                    </div>
                    <div class="form-group">
                        <label for="ramo">Ramo</label>
                        <div class="flex flex-wrap gap-4 my-2" rules="required">
                            <Field type="radio" name="ramo" value="D" /> DANNI
                            <Field type="radio" name="ramo" value="V" /> VITA
                        </div>
                        <div class="form-error">
                            <ErrorMessage name="ramo" />
                        </div>
                    </div>


                    <div class="form-group md:col-span-3">
                        <div class="w-full">
                            <strong>N.B:</strong> Le richieste effettuate entro il giorno <strong>20</strong>
                            (compreso)
                            del
                            mese avranno valenza <strong>sul mese contabile in corso</strong>.
                            Quelle effettuate <strong>dal giorno 21</strong> avranno valenza <strong>sul mese
                                contabile
                                successivo</strong>.
                        </div>
                        <div class="w-full">
                            <label for="descrizione">Descrizione estesa:</label>
                            <Field v-slot="{ field, errors }" name="descrizione">
                                <textarea v-bind="field" name="descrizione" />
                            </Field>
                            <div class="form-error">
                                <ErrorMessage name="descrizione" />
                            </div>
                        </div>
                    </div>

                    <div class="form-group flex ">
                        <label class="x-col-3-sm md:mr-10">Allegato (opzionale):</label>
                        <div class="text-center">
                            <input id="contributiAttachment" type="file" ref="upload" name="file" accept=".*"
                                url="api/apps/contributi/contributi/attachment2">
                        </div>
                    </div>

                    <section class="md:col-span-3 w-full flex justify-between px-3">
                        <div>
                            <span v-if="type == 'EDIT'">
                                Status:
                                <span v-if="contributo.status == 'DRAFT'" class="x-label">IMPEGNATO</span>
                                <span v-if="contributo.status == 'PENDING'" class="x-label x-yellow">IN
                                    ATTESA</span>
                                <span v-if="contributo.status == 'ACTIVE'" class="x-label x-green">EROGATO</span>
                                <span v-if="contributo.status == 'DENIED'" class="x-label x-red">RIFIUTATO</span>
                                <span v-if="contributo.status == 'DELETED'" class="x-label x-red">CANCELLATO</span>
                            </span>
                            <span v-if="form?.importo?.error" class="x-error">
                                <span v-if="error?.messages?.includes('BUDGET_ESAURITO')">
                                    Budget <b>ESAURITO</b> o <b>NON SUFFICIENTE</b>
                                </span>
                            </span>
                        </div>
                        <div class="flex space-x-2 justify-end">

                            <button v-if="UI == 'DIREZIONE' && type == 'JOLLY'" class="btn primary"
                                @click="() => submitForm()">
                                <PhFloppyDisk :size="16" />
                                Eroga
                            </button>
                            <button v-if="UI == 'DIREZIONE' && type == 'EDIT'" class="btn primary">
                                <PhFloppyDisk :size="16" />
                                Salva
                            </button>
                            <button v-if="(UI == 'AREAMGR' || UI == 'DISTRICTMGR') && contributo.budget == 'DIREZ'"
                                @click="() => editStatus('PENDING') && submitForm()" class="btn primary">
                                <PhFloppyDisk :size="16" />
                                Richiedi
                            </button>
                            <button
                                v-if="(UI == 'AREAMGR' && contributo.budget == 'AREA') || (UI == 'DISTRICTMGR' && contributo.budget == 'DISTRICT')"
                                @click="() => editStatus('ACTIVE') && submitForm()" class="btn primary">
                                <PhFloppyDisk :size="16" />
                                Eroga
                            </button>

                            <button
                                v-if="((UI == 'AREAMGR' && contributo.budget == 'AREA') || (UI == 'DISTRICTMGR' && contributo.budget == 'DISTRICT'))
                                    && (type=='ADD' || type=='EDIT')"
                                @click="() => editStatus('DRAFT') && submitForm()" class="btn primary">
                                <PhFloppyDisk :size="16" />
                                <span v-if="type == 'ADD'">Impegna</span>
                                <span v-if="type == 'EDIT'">Salva come IMPEGNATO</span>
                            </button>

                            <button type="button" @click="() => { abort() }" class="btn neutral p-0">
                                <PhX :size="16" /> Annulla
                            </button>

                            <Field type="hidden" name="anno" :value="selectedYear" />
                        </div>
                    </section>

                </div>
            </div>


        </Form>
    </div>

</template>
