import MyPage from "@/apps/my-page/MyPage.vue";
import List from "@/apps/my-page/List.vue";
import AgencyDetail from "@/apps/my-page/agency-detail/AgencyDetail.vue";
import AgencyTeam from "@/apps/my-page/agency-team/AgencyTeam.vue";
import Privacy from "@/apps/my-page/privacy/Privacy.vue";
import {checkPrivacyGuard} from "@/apps/my-page/privacyGuard.js";
import router from "@/_common/router/index.js";
import {useUserStore} from "@/_common/stores/user.js";

export default [
    {
        path: '/my-page',
        name: 'my-page',
        component: MyPage,
        meta: {
            breadcrumb: 'MyPage',
            abstract: true
        },
        children: [
            {
                path: 'list',
                component: List,
                name: 'my-page-agencies',
                meta: {
                    breadcrumb: 'Elenco Agenzie'
                },
                beforeEnter: (to, from) => {
                    const authData = useUserStore();
                    if ( authData.UTYPE === 'AGENTE' ) {
                        router.push({ path: `/my-page/${authData.AGENZIA}/dettaglio` })
                    }
                },
            },
            {
                path: ':id',
                meta: {
                    breadcrumb: 'Dettaglio',
                    abstract: true,
                    skip: true
                },
                children: [
                    {
                        path: 'dettaglio',
                        component: AgencyDetail,
                        name: 'my-page-detail',
                        meta: {
                            breadcrumb: 'Anagrafica'
                        },
                        beforeEnter: checkPrivacyGuard
                    },
                    {
                        path: 'team',
                        component: AgencyTeam,
                        name: 'my-page-team',
                        meta: {
                            breadcrumb: 'Team'
                        },
                        beforeEnter: checkPrivacyGuard
                    }
                ]
            }
        ]
    },
    {
        path: '/privacy',
        name: 'privacy',
        component: Privacy
    }
];