<script setup>
import { ref, defineExpose, defineEmits } from 'vue';
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
} from '@headlessui/vue'
import { toast } from "vue3-toastify";
import { Form, Field, ErrorMessage } from "vee-validate";
import { agitApi } from '@/apps/agit/api/agit';
import * as yup from 'yup';
import { useUserStore } from "@/_common/stores/user.js";
import { statsApi } from "@/_common/api/stats.js";
import { InputNumber } from 'primevue';

const emit = defineEmits(['close']);

const authData = useUserStore();

let polizza = ref({});
let isOpen = ref(false);

const overwritePolizza = {
  anno: new Date().getFullYear(),
  createdAt: new Date().toLocaleString("en-US", { timeZone: "Europe/Rome" }),
  lastUpdate: new Date().toLocaleString("en-US", { timeZone: "Europe/Rome" }),
}

const onSubmit = async (values, { resetForm }) => {
  console.debug('values', values);
  const res = await agitApi.upsert({ agenzia_id: authData.AGENZIA, ...values, ...overwritePolizza }, values?.id);
  if (!res?.success) {
    return toast.error('Si è verificato un errore.');
  }
  if (!values?.id) {
    statsApi.registerEvent('agit', 'insert');
  }
  toast.success('Polizza salvata con successo.');
  closeModal();
  setTimeout(() => {
    polizza.value = {}
    resetForm();
  }, 500);
};

async function openModal(row) {
  if (row?.id) {
    const res = await agitApi.getPolizza(row?.id);
    if (!res?.success) {
      return toast.error("Si è verificato un errore durante il caricamento dell'utente.");
    }
    polizza.value = { ...res.data };
  }
  isOpen.value = true
}

function closeModal() {
  isOpen.value = false
  setTimeout(() => {
    polizza.value = {}
  }, 300);
  emit('close');
}

const schema = yup.object({
  annoFiscale: yup
    .number().typeError("Anno polizza deve essere un numero")
    .required("Anno polizza è obbligatorio")
    .min(1900, "Anno polizza deve essere maggiore di 1900")
    .max(2300, "Anno polizza deve essere minore di 2300")
    .transform((value, originalValue) => (/\s/.test(originalValue) ? NaN : value)),
  numero: yup
    .number()
    .typeError("Numero polizza deve essere un numero")
    .required('Numero polizza è obbligatorio'),
  intestatario: yup
    .string()
    .required('Nome e Cognome dell\'intestatario è obbligatorio'),
  codiceFiscale: yup
    .string()
    .length(16, 'Codice fiscale deve essere di 16 caratteri')
    .required('Codice fiscale dell\'intestatario è obbligatorio'),
  codiceTariffa: yup
    .string()
    .required('Codice Tariffa è obbligatorio'),
  tipologia: yup
    .string()
    .required('Tipologia è obbligatorio'),
  premio: yup
    .string()
    .required("Il Premio è obbligatorio")
  //.number().typeError('Il Premio deve essere un numero')
});

defineExpose({
  openModal,
  closeModal
})

</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0" style="background-color: rgba(24,27,49,0.9)" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <div class="card transition-all overflow-hidden max-w-md w-full">
              <div class="card-header">
                Modifica polizza
              </div>
              <div class="card-body p-5">
                <Form ref="form" @submit="onSubmit" :initial-values="polizza" v-slot="{ errors }"
                  :validation-schema="schema">

                  <div class="form-group">
                    <label for="annoFiscale">
                      Anno polizza:
                    </label>
                    <Field name="annoFiscale" v-model="polizza.annoFiscale" type="text"
                      :class="{ 'has-error': errors.annoFiscale }" />
                    <div class="form-error">
                      <ErrorMessage name="annoFiscale" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="numero">
                      Numero polizza:
                    </label>
                    <Field name="numero" v-model="polizza.numero" type="text" :class="{ 'has-error': errors.numero }" />
                    <div class="form-error">
                      <ErrorMessage name="numero" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="intestatario">
                      Nome e Cognome dell'intestatario:
                    </label>
                    <Field name="intestatario" v-model="polizza.intestatario" type="text"
                      :class="{ 'has-error': errors.intestatario }" />
                    <div class="form-error">
                      <ErrorMessage name="intestatario" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="codiceFiscale">
                      Codice fiscale dell'intestatario:
                    </label>
                    <Field name="codiceFiscale" v-model="polizza.codiceFiscale" type="text"
                      :class="{ 'has-error': errors.codiceFiscale }" />
                    <div class="form-error">
                      <ErrorMessage name="codiceFiscale" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="tipologia">
                      Tipologia intestatario
                    </label>
                    <div class="flex flex-col  justify-center pt-1">
                      <div>
                        <Field name="tipologia" type="radio" value="TITOLARE" class="" /> Titolare o contitolare di
                        Agenzia o suo
                        familiare
                      </div>
                      <div>
                        <Field name="tipologia" type="radio" value="DIPENDENTE" /> Dipendente di Agenzia
                      </div>
                      <div>
                        <Field name="tipologia" type="radio" value="PROCURATORE" /> Procuratore di Agenzia
                      </div>
                      <div>
                        <Field name="tipologia" type="radio" value="SP" /> Agenzia costituita in società di
                        persone:
                        proprietario di quote di società o suo familiare
                      </div>
                      <div>
                        <Field name="tipologia" type="radio" value="SC" /> Agenzia costituita in società di
                        capitali:
                        delegato alle attività agenziali e/o il legale rappresentante, o suo familiare
                      </div>
                    </div>
                    <div class="form-error">
                      <ErrorMessage name="tipologia" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="codiceTariffa">
                      Codice Tariffa:
                    </label>
                    <Field name="codiceTariffa" v-model="polizza.codiceTariffa" type="text"
                      :class="{ 'has-error': errors.codiceTariffa }" />
                    <div class="form-error">
                      <ErrorMessage name="codiceTariffa" />
                    </div>
                  </div>

                  <div class="form-group">
                    <label>
                      <Field name="versamentoAggiuntivo" v-model="polizza.versamentoAggiuntivo" type="checkbox"
                        :class="{ 'has-error': errors.versamentoAggiuntivo }" value="1" unchecked-value="0" />
                      Versamento Aggiuntivo
                    </label>
                  </div>

                  <div class="form-group">
                    <label for="premio">
                      Premio
                    </label>
                    <Field name="premio" v-model="polizza.premio" type="text" :class="{ 'has-error': errors.premio }">
                      <InputNumber v-model="polizza.premio" inputId="premio-it" locale="it-IT" :minFractionDigits="2"
                        fluid />
                    </Field>

                    <div class="form-error">
                      <ErrorMessage name="premio" />
                    </div>
                  </div>

                  <div class="text-right mt-5">
                    <button type="button" class="btn secondary mr-2"
                      @click="(e) => e.preventDefault && closeModal()">Chiudi</button>
                    <button type="submit" class="btn success">Salva</button>
                  </div>

                </Form>
              </div>
            </div>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
