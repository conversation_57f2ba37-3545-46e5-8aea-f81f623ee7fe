import AccordoEconomico from "@/apps/accordo-economico/AccordoEconomico.vue";
import Summary from "@/apps/accordo-economico/Summary.vue";
import Detail from "@/apps/accordo-economico/Detail.vue";
import SetupObj from "@/apps/accordo-economico/SetupObj.vue";
import Agreements from "@/apps/accordo-economico/Agreements.vue";
import AgencyProfile from "@/apps/accordo-economico/AgencyProfile.vue";
import {useUserStore} from "@/_common/stores/user.js";

export default [
    {
        beforeEnter: (to, from) => {
            const authData = useUserStore();
            if ( ![ 'KA', 'AMMINISTRATORE', 'DISTRICTMGR' ].includes(authData.UTYPE) ) {
                window.location.href = '../';
            }
        },
        path: '/accordo-economico/:year?',
        name: 'accordo',
        component: AccordoEconomico,
        meta: {
            breadcrumb: 'MyPage',
            abstract: true
        },
        redirect: { name: 'summary' },
        children: [
            {
                path: 'sintesi',
                name: 'summary',
                component: Summary,
                props: true,
                meta: {
                    label: 'Sintesi'
                }
            },
            {
                path: 'dettaglio',
                name: 'detail',
                component: Detail,
                props: true,
                meta: {
                    label: 'Dettaglio'
                }
            },
            {
                path: 'scheda-agenzia/:agenzia_id?-:revisionId?',
                name: 'profile',
                component: AgencyProfile,
                props: true,
                meta: {
                    label: 'Scheda Agenzia'
                }
            },
            {
                path: 'setup-obj',
                name: 'setup',
                component: SetupObj,
                props: true,
                meta: {
                    label: 'Setup Obj'
                }
            }
        ]
    }
];