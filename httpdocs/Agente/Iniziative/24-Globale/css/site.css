/*CSS Document*/
BODY
{
	margin: 0px;
	padding: 0px;
	background-color: #FFFFFF;
/*
background-attachment:scroll;
background-image:url(/Agente/Iniziative/7-ContaPunti/images/sfondo.gif);
background-position:center top;
background-repeat:repeat-x;
*/
	behavior: url(/Agente/Iniziative/24-Globale/js/csshover3.htc);
}
IMG
{
	display: block;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
}
.img_text
{
	display: inline;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
OBJECT, EMBED
{
	display: block;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
}
HTML, BODY
{
	height: 100%;
}
DIV
{
	font-family: Arial;
	font-size: 12px;
}
/*BASE STYLE*/
UL
{
	margin-top: 0px;
	margin-bottom: 0px;
}
LI
{
	margin-top: 0px;
	margin-bottom: 0px;
}
SELECT
{
	font-family: Arial;
	font-size: 12px;
}
.float_none
{
	float: none;
}
TABLE
{
	border: 0px solid #000000;
	padding: 0px;
	margin: 0px;
}
TR
{
	border: 0px solid #000000;
	padding: 0px;
	margin: 0px;
}
TD
{
	border: 0px solid #000000;
	padding: 5px 10px;
	margin: 0px;
}
.clear
{
	clear: left;
}
.float_left
{
	float: left;
}
.centered
{
	display: block;
	margin: 0 auto;
}
A:link
{
	color: #FFFFFF;
	text-decoration: none;
}
A:visited
{
	color: #FFFFFF;
	text-decoration: none;
}
A:active
{
	color: #FFFFFF;
	text-decoration: none;
}
A:hover
{
	color: #FFFFFF;
	font-weight: bold;
}
.intro_link:link
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:visited
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:active
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:hover
{
	color: #FFFFFF;
	font-weight: bold;
}
.spacer_5
{
	height: 5px;
}
.spacer_10
{
	height: 10px;
}
.spacer_20
{
	height: 20px;
}
.spacer_30
{
	height: 30px;
}
/*FONTS*/
.yellow
{
	color: #FFC200;
}
.white_bold
{
	color: #FFF;
	font-weight: bold;
}
.font_18
{
	font-size: 18px;
}
.black_18_bold
{
	font-size: 18px;
	font-weight: bold;
	color: #000;
}
.black_16_bold
{
	font-size: 16px;
	font-weight: bold;
	color: #000;
}
.font_gray
{
	color: #636466 !important;
}
/*MAIN TEMPLATE*/
#contents
{
	float: left;
	width: 869px;
	height: 492px;
	background-color: #FFFFFF;
	border-top: 8px solid #E7E7E7;
	border-bottom: 8px solid #E7E7E7;
	border-left: 8px solid #E7E7E7;
	border-right: 8px solid #E7E7E7;
	overflow: hidden;
}

#center_frame_container
{
	float: left;
	width: 884px;
	height: 507px;
	overflow: hidden;
	position: relative;
	background-color: #E6E7E7;
	border-left: 1px solid #E6E7E7;
	border-top: 1px solid #E6E7E7;
}
#content_container
{
	float: left;
	width: 868px;
	height: 491px;
	top: 8px;
	left: 8px;
	overflow: hidden;
	position: relative;
}

#background1
{
	float: left;
	width: 868px;
	height: 491px;
	overflow: hidden;
	position: relative;
	background-image: url(/Agente/Iniziative/24-Globale/images/background4.png);
}
#logo
{
	float: left;
	width: 144px;
	height: 144px;
	overflow: hidden;
	position: absolute;
	top: 91px;
	left: 61px;
	background-image:url(/Agente/Iniziative/24-Globale/images/logo4.png);
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
#infos
{
	float: left;
	width: 258px;
	height: 129px;
	overflow: hidden;
	position: absolute;
	top: 241px;
	left: 6px;
	line-height: 16px;
	font-size: 14px;
	color: #000;
	font-weight: normal;
	text-align: center;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
#reg_button
{
	float: left;
	width: 121px;
	height: 27px;
	overflow: hidden;
	position: absolute;
	top: 377px;
	left: 73px;
	background-image:url(/Agente/Iniziative/24-Globale/images/reg_button.png);
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
	cursor: pointer;
}
#reg_button:hover
{
	background-image:url(/Agente/Iniziative/24-Globale/images/reg_button_roll.png);
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
#table_info1
{
	float: left;
	width: 234px;
	height: 34px;
	overflow: hidden;
	position: absolute;
	top: 92px;
	left: 314px;
	line-height: 16px;
	font-size: 14px;
	color: #000;
	font-weight: bold;
	text-align: left;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
#table_info2
{
	float: left;
	width: 222px;
	height: 34px;
	overflow: hidden;
	position: absolute;
	top: 92px;
	left: 550px;
	line-height: 14px;
	font-size: 12px;
	color: #000;
	font-weight: normal;
	text-align: right;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
#table_header
{
	float: left;
	width: 470px;
	height: 28px;
	overflow: hidden;
	position: absolute;
	top: 130px;
	left: 306px;
	background-color: #DCDCDC;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
.dp_grid_1
{
	float: left;
	width: 32px;
	text-align: center;
	font-weight: bold;
	font-size: 10px;
	color: #636466;
	
}
.dp_grid_2
{
	float: left;
	width: 43px;
	text-align: center;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
.dp_grid_3
{
	float: left;
	width: 115px;
	text-align: left;
	padding: 0 0 0 10px;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
.dp_grid_4
{
	float: left;
	width: 60px;
	text-align: center;
	padding: 0 0 0 0px;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
.dp_grid_4a
{
	float: left;
	width: 70px;
	text-align: center;
	padding: 0 0 0 0px;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
.dp_grid_4b
{
	float: left;
	width: 70px;
	text-align: center;
	padding: 0 0 0 0px;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
.dp_grid_5
{
	float: left;
	width: 70px;
	text-align: center;
	font-weight: bold;
	font-size: 10px;
	color: #636466;	
}
#table_info3
{
	float: left;
	width: 199px;
	height: 14px;
	overflow: hidden;
	position: absolute;
	top: 415px;
	left: 519px;
	line-height: 12px;
	font-size: 10px;
	color: #C5C6C7;
	font-weight: normal;
	text-align: right;
	behavior: url(/Agente/Iniziative/24-Globale/js/iepngfix.htc);
}
/*FULLPAGE GRID*/
#fullpage_grid
{
	position: absolute;
	top: 159px;
	left: 306px;
	width: 501px;
	height: 242px;
	overflow: hidden;
	clear: both;
}

.fullpage_grid_row
{
	width: 470px;
	height: 17px;
	color: #000000;
	font-weight: bold;
	border-bottom: 2px solid #A6A7A9;
	clear: both;
}

.fullpage_grid_row_blue
{
	width: 470px;
	height: 17px;
	color: #006FA7;
	font-weight: bold;
	border-bottom: 2px solid #A6A7A9;
	clear: both;
}

.fullpage_grid_row_red
{
	width: 470px;
	height: 17px;
	color: #9F0000;
	font-weight: bold;
	border-bottom: 2px solid #A6A7A9;
	clear: both;
}

.fullpage_grid_row DIV
{
	line-height: 17px;
	font-size: 10px;
}


.fullpage_grid_row_blue DIV
{
	line-height: 17px;
	font-size: 10px;
}

.fullpage_grid_row_red DIV
{
	line-height: 17px;
	font-size: 10px;
}

.dp_grid_field_1
{
	float: left;
	width: 32px;
	text-align: center;
	
}
.dp_grid_field_2
{
	float: left;
	width: 43px;
	text-align: center;
}
.dp_grid_field_3
{
	float: left;
	width: 115px;
	text-align: left;
	padding: 0 0 0 10px;
}
.dp_grid_field_4
{
	float: left;
	width: 60px;
	text-align: center;
	padding: 0 0 0 0px;
}
.dp_grid_field_4a
{
	float: left;
	width: 70px;
	text-align: center;
	padding: 0 0 0 0px;
}
.dp_grid_field_4b
{
	float: left;
	width: 70px;
	text-align: center;
	padding: 0 0 0 0px;
}
.dp_grid_field_5
{
	float: left;
	width: 70px;
	text-align: center;
}

.star
{
	width: 70px;
	height: 17px;
	background-image:url(/Agente/Iniziative/24-Globale/images/star.png);	
	behavior: url('/Agente/Iniziative/24-Globale/js/iepngfix.htc');		
}
