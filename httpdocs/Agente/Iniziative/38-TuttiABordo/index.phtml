<?

if(isset($_GET['p']))
{
	$polizze = $_GET['p'];
}
else
{
	$polizze = $Status->polizze_MS+$Status->polizze_DIM;
}

$polizze_left_1 = 10-$polizze;
$polizze_left_2 = 20-$polizze;

$polizze_js = $polizze;

if($polizze_js > 10)
	$polizze_js = 10;



$bonus_base_MS = 10;
$bonus_base_DIM = 5;

if($Status->obiettivoOK)
{
	$bonus_MS = $bonus_base_MS;
	$bonus_DIM = $bonus_base_DIM;
}
else
{
	$bonus_MS = 0;
	$bonus_DIM = 0;
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Tutti a bordo" />
<link href="/Agente/Iniziative/38-TuttiABordo/css/site.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/Agente/Iniziative/38-TuttiABordo/js/fx.js"></script>
<script type="text/javascript" src="/Agente/Iniziative/38-TuttiABordo/js/status.js"></script>

<title>Tutti a bordo</title>
</head>
<body onload="startEffects(<?=$polizze_js?>);" >
	<div id="contents">
		<div id="menu">
			<a href="/Agente/Iniziative/38-TuttiABordo/">
				<div id="menu_1" class="menu_sel">Status
				</div>
			</a><!-- ?m=35000000&a=15000&pa=50 -->
			<a href="/Agente/Iniziative/38-TuttiABordo/dettaglioPolizze">
				<div id="menu_2" >Dettaglio Polizze
				</div>
			</a>
			<a href="file/RegolamTuttiaBordo.pdf">
				<div id="menu_6">Regolamento
				</div>
			</a>
		</div>
		<div id="center">


			<div id="periscopio_cont">
				<div id="periscopio_tube">
				</div>

				<div id="periscopio">
					<div id="tube_dirty_1">
					</div>
					<div id="tube_dirty_2">
					</div>
					<div id="tube_dirty_3">
					</div>
					<div id="tube_dirty_4">
					</div>
					<div id="fondomare_mask">
						<div id="fondomare">
							<div id="onde">
							</div>
						</div>

						<div id="sub">
						</div>
						<div id="left_h_cont">
							<div id="left_h">
							</div>
						</div>
						<div id="right_h_cont">
							<div id="right_h">
							</div>
						</div>
					</div>
					<div id="periscopio_bg">
					</div>



					<div id="periscopio_left_close">
					</div>
					<div id="periscopio_right_close">
					</div>

					<div id="periscopio_left_open">
					</div>
					<div id="periscopio_right_open">
					</div>

					<div id="periscopio_ago">
					</div>

				</div>

			</div>




			<div id="medal">
			</div>

			<div id="info">
			</div>
			<div id="summary">
				<div id="sum_1"><?=$Status->polizze_MS?>
				</div>
				<div id="sum_2"><?=$bonus_base_MS?>
				</div>
				<div id="sum_3"><?=number_format($Status->premi_MS,0,',','.') ?>
				</div>
				<div id="sum_4"><?=$Status->polizze_DIM?>
				</div>
				<div id="sum_5"><?=$bonus_base_DIM?>
				</div>
				<div id="sum_6"><?=number_format($Status->premi_DIM,0,',','.') ?>
				</div>
				<div id="sum_7">
					Attuale Importo&nbsp;&nbsp;&nbsp;&nbsp;<br/>
					Erogabile&nbsp;&nbsp;&nbsp;&nbsp;<br/>
					<span class="sum_7_spacer"><br/></span>
					<span id="sum_7_1">
						€ (<?=number_format($Status->premi_MS,0,',','.') ?> x <?=$bonus_MS?>%) <span class="sum_7_simbol">+</span><br/>
						€ (<?=number_format($Status->premi_DIM,0,',','.') ?> x <?=$bonus_DIM?>%) <span class="sum_7_simbol">=</span>
					</span>
				</div>
				<div id="sum_8"><?=number_format($Status->importoErogabile,0,',','.') ?>
				</div>
			</div>

			<?
			if($Status->obiettivoOK)
			{
			?>
				<div id="baloon" class="baloon_5_l">
						Missione compiuta,<br/>
						si emerge!<br/>
						Raggiunto il Bonus<br/>
						ora conviene incrementare<br/>
						la produzione computabile
				</div>
			<?
			}
			else
			{
			?>
				<div id="baloon" class="baloon_5_l">
					Manca<?=$polizze_left_1==1 ? '' : 'no'?> <?=$polizze_left_1?> contratt<?=$polizze_left_1==1 ? 'o' : 'i'?><br/>
					per emergere<br/>
					con i Bonus<br/>
					di incentivazione...<br/>
					ai posti di manovra!
				</div>
			<?
			}
			?>
			<div id="info2">
			</div>
			<div id="nb">
				<b>NB:</b> per il calcolo della produzione<br/>
				computabile e dell’importo di<br/>
				incentivazione erogabile faranno<br/>
				fede i dati ufficiali di Compagnia.
			</div>

		</div>
	</div>
</body>
</html><? echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>