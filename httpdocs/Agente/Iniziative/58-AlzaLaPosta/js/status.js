var debug = 0; 
var myDelay = 15;
var myTimeout = 500;


var polizze_anim_step = 167;
var n_polizze_done = 0;
var n_polizze_anim = 0;

var actual_month = 0;
var month_start = 4;
var month_end = 12;


/* bezier functions*/
//====================================\\
// 13thParallel.org Bezi�r Curve Code \\
//   by <PERSON> (www.pupius.net)   \\
//====================================\\

coord = function (x,y) {
  if(!x) var x=0;
  if(!y) var y=0;
  return {x: x, y: y};
}

function B1(t) { return t*t*t }
function B2(t) { return 3*t*t*(1-t) }
function B3(t) { return 3*t*(1-t)*(1-t) }
function B4(t) { return (1-t)*(1-t)*(1-t) }

function getBezier(percent,C1,C2,C3,C4) {
  var pos = new coord();
  pos.x = C1.x*B1(percent) + C2.x*B2(percent) + C3.x*B3(percent) + C4.x*B4(percent);
  pos.y = C1.y*B1(percent) + C2.y*B2(percent) + C3.y*B3(percent) + C4.y*B4(percent);
  return pos;
}



/* general effects*/
opacityEnter = {type: 'opacity', from:0, to:100, step: 1, delay:10}



function startEffects(npd,am)
{
	n_polizze_done = npd;
	actual_month = am;
	//alert("Debug Cannon Ball");
	
	setTimeout(effect1, myTimeout);
	//setTimeout(setCurvePos, myDelay*2);
}


function effect1()
{
	
	//alert("Debug Cannon Ball");
	showDiv("catapulta");
	setTimeout(effect2, myTimeout);

}

function effect2()
{
	
	//alert("Debug Cannon Ball");
	showDiv("catapulta_cont");
	setTimeout(effect3, myTimeout);

}

function effect3()
{
	
	//alert("Debug Cannon Ball");
	showDiv("gruppo_info_title");
	showDiv("gruppo_info");
	setTimeout(effect4, myTimeout);

}

function effect4()
{
	
	//alert("Debug Cannon Ball");
	showDiv("confalone_cont");
	setTimeout(effect5, myTimeout);

}

function effect5()
{
	if(n_polizze_anim <= n_polizze_done)
	{
		//alert("Debug Cannon Ball");
		myGetElementById("confalone").style.left = '-'+(polizze_anim_step*n_polizze_anim)+'px';
		setTimeout(effect5, 250);
		n_polizze_anim++;
	}
	else
	{
		setTimeout(effect6, myTimeout);
	}
	

}


function effect6()
{
	
	//alert("Debug Cannon Ball");
	showDiv("status_info_1");
	setTimeout(effect7, myTimeout);

}



function effect7()
{
	if( (month_start < actual_month ) && month_start <= month_end)
	{
		showDiv("month_cont_"+month_start);
		month_start++;
		setTimeout(effect7, myTimeout);
	}
	else
	{
		setTimeout(effect8, myTimeout);
	}
}



function effect8()
{
	
	//alert("Debug Cannon Ball");
	//showDiv("catapulta_cont");
	//setTimeout(effect7, myTimeout);
	myGetElementById("catapulta").style.left = '-326px';
	
	effect8b();
	//setTimeout(effect8b, myDelay);
}
function effect8b()
{
	
	//alert("Debug Cannon Ball");
	//showDiv("catapulta_cont");
	//setTimeout(effect7, myTimeout);
	showDiv("ball_cont");
	setCurvePos();
}


var C4 = coord(280,6); //punto INIZIO
var C3 = coord(600,6); // control point INIZIO
var C1 = coord(764,287); // punto di FINE
var C2 = coord(750,30); // contol point FINE

var percent = 0;
var p_step = 1;

function setCurvePos()
{
	if(percent <= 100)
	{
		var point = getBezier((percent/100),C1,C2,C3,C4);
		// move ball into bezier curve
		getDiv('ball_cont').style.top = point.y+'px';
		getDiv('ball_cont').style.left = point.x+'px';
		
		// reduce ball
		getDiv('ball_img').style.width = (53-(Math.round(53*percent/140)))+'px';
		
		percent = percent+p_step;
		setTimeout(setCurvePos, myDelay*2);
	}
	else
	{
		var point = getBezier(1,C1,C2,C3,C4);
		getDiv('ball_cont').style.top = point.y+'px';
		getDiv('ball_cont').style.left = point.x+'px';
		
		getDiv('ball_img').style.width = (53-(Math.round(53*percent/140)))+'px';
		
		effect9();
	}	
}

function effect9()
{
	
	//alert("Debug Cannon Ball");
	//showDiv("castello");
	//setTimeout(effect7, myTimeout);
	myGetElementById("castello").style.left = '-179px';
	showDiv("status_final");
	setTimeout(effect10, myTimeout);
}

function effect10()
{
	showDiv("month_cont_"+month_start);
	
	setTimeout(effect11, myTimeout);
}
function effect11()
{
	showDiv("status_info_2");
	
	setTimeout(effect12, myTimeout);
}

function effect12()
{
	showDiv("nb");
	
	//setTimeout(effect13, myTimeout);
}

/*
function effect1_1()
{
	showDiv('raccolta_title');
	showDiv('baloon_1');
	setTimeout(effect1_2, myTimeout*2);
}
function effect1_2()
{
	showDiv('status_note');
	setTimeout(effect1_3, myTimeout*2);
}
function effect1_3()
{
	showDiv('point_shadow');
	showDiv('point');
	showDiv('v_line');
	setTimeout(effect1_4, myTimeout*2);
}
function effect1_4()
{
	showDiv('bonus_table');
	setTimeout(effect2, myTimeout*2);
}

function effect2()
{

	statusBarEnd = Math.round( ( 100 * milions / 5000000));
	
	
	//alert(statusBarEnd);
	getDiv('h_line').style.width = (800-statusBarEnd)+'px';
	getDiv('h_line').style.left = (14+statusBarEnd)+'px';
	
	//alert("comincia status_bar: 0 to "+statusBarEnd);
	
	moveStatus = {type: 'width',from: 12, to: statusBarEnd+12, step: 2, delay: myDelay}
	
	moveLine = {type: 'left',from: 10, to: statusBarEnd+10, step: 2, delay: myDelay}
	movePointer = {type: 'left',from: -3, to: statusBarEnd-3, step: 2, delay: myDelay}


	var statuBar = getDiv('status_bar');
	
	var v_line = getDiv('v_line');
	
	var point_shadow = getDiv('point_shadow');
	var point = getDiv('point');
	
	showDiv('status_bar');
	
	$fx(statuBar).fxAdd(moveStatus).fxRun(effect3pause);
	
	$fx(v_line).fxAdd(moveLine).fxRun();
	$fx(point_shadow).fxAdd(movePointer).fxRun();
	$fx(point).fxAdd(movePointer).fxRun();
}
function effect3pause()
{
	setTimeout(effect3, myTimeout*2);
}
function effect3()
{
	showDiv('milions_value');
	setTimeout(effect4, myTimeout*2);
	//alert("debug 0");
}
function effect4()
{	

	showDiv('h_line');
	
	//alert("debug 1");
	
	var agencyBarEnd = Math.round( ( 40 * agency / 5000));
	
	move_v_line = {type: 'height',from: 266, to: 266-agencyBarEnd, step: -2, delay: myDelay}
	
	//alert("debug 2");
	moveLine = {type: 'top',from: 328, to: 328-agencyBarEnd, step: -2, delay: myDelay}
	movePoint = {type: 'top',from: 314, to: 314-agencyBarEnd, step: -2, delay: myDelay}
	
	//alert("debug 3");
	
	var h_line = getDiv('h_line');
	
	//alert("debug 4");
	
	var v_line = getDiv('v_line');
	var point = getDiv('point');

	$fx(h_line).fxAdd(moveLine).fxRun(effect5pre);
	$fx(point).fxAdd(movePoint).fxRun();
	$fx(v_line).fxAdd(move_v_line).fxRun();
}
function effect5pre()
{
	setTimeout(effect5, myTimeout*2);
}
function effect5()
{
	getDiv('bonus_table').style.width = '320px';
	getDiv('bonus_table').style.height = '38px';
	setTimeout(effect9, myTimeout*2);
}
function effect9()
{
	showDiv('final_1');
	setTimeout(effect10, myTimeout*2);
}
function effect10()
{
	getDiv('bonus_table').style.height = '71px';
	setTimeout(effect11, myTimeout);
}
function effect11()
{
	showDiv('final_2');
	setTimeout(effect12, myTimeout);
}

function effect12()
{
	getDiv('bonus_table').style.height = '93px';
	setTimeout(effect13, myTimeout);
}
function effect13()
{
	showDiv('final_3');
	setTimeout(effect14, myTimeout);
}
function effect14()
{
	getDiv('bonus_table').style.height = '130px';
	setTimeout(effect15, myTimeout);
}
function effect15()
{
	showDiv('final_4');
	setTimeout(effect16, myTimeout*2);
}


function effect16()
{
	getDiv('bonus_table').style.width = '495px';
	setTimeout(effect17, myTimeout);
}


function effect17()
{
	showDiv('final_6_1');
	showDiv('final_7_1');
	showDiv('final_8_1');
	showDiv('final_6_2');
	showDiv('final_7_2');
	showDiv('final_8_2');
	showDiv('final_6_3');
	showDiv('final_7_3');
	showDiv('final_8_3');
	setTimeout(effect18, myTimeout*2);
}

function effect18()
{
	showDiv('super_bonus_table');
	getDiv('bonus_table').style.width = '590px';
	setTimeout(effect19, myTimeout);
}
function effect19()
{
	showDiv('final_9');
	showDiv('final_10');
	showDiv('final_11');
	setTimeout(effect20, myTimeout*2);
}

function effect20()
{
	getDiv('bonus_table').style.width = '713px';
	setTimeout(effect21, myTimeout);
}
function effect21()
{
	showDiv('final_12');
	setTimeout(effect22, myTimeout*2);
}

function effect22()
{
	getDiv('bonus_table').style.width = '877px';
	setTimeout(effect23, myTimeout*2);
}
function effect23()
{
	hideDiv('cover');
	setTimeout(effect24, myTimeout);
}
function effect24()
{
	showDiv('final_13');
	showDiv('super_controller');
	hideDiv('super_bonus_table');
	getDiv('super_bonus_table').style.width = '877px';
	getDiv('super_bonus_table').style.height = '130px';	
}

*/


/* general functions */
function myGetElementById(id_name)
{
	if (document.getElementById && !window.opera)
	{
	  var obj = document.getElementById(id_name);         
	}
	else if(document.all)
	{
	  var obj = document.all.id_name;
	}   	
	
	return obj;		
}

function showDiv(divName)
{
	myGetElementById(divName).style.display = 'inline';
}

function hideDiv(divName)
{
	myGetElementById(divName).style.display = 'none';
}

function getDiv(divName)
{
	return myGetElementById(divName);
}

function myAlert(myMex)
{
	if(debug)
		alert(myMex);	
}