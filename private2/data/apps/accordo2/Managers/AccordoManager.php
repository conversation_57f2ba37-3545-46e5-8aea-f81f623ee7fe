<?php

namespace data\apps\accordo2\Managers;


use data\apps\accordo2\Models\Accordo;
use data\apps\accordo2\Models\AccordoRev;
use data\apps\accordo2\Repositories\AccordoRepository;
use data\apps\accordo2\Repositories\AccordoRevRepository;
use data\apps\accordo2\Repositories\AgencyRepository;
use data\apps\accordo2\Repositories\FasciaRamiPrefRepository;
use data\apps\accordo2\Repositories\FasciaVitaRepository;
use data\apps\accordo2\Repositories\UserRepository;
use metadigit\core\CoreTrait;
use metadigit\core\db\PdoTrait;

class AccordoManager
{
    use PdoTrait, CoreTrait;

    public static $DEADLINE_SOFT = "2025-09-16";
    public static $DEADLINE_HARD = "2025-09-23";

    protected static $LIVELLI_PERSONALIZZAZIONE = 4;

    /**
     * @var AccordoRepository
     */
    protected $accordoRepository;

    /**
     * @var AccordoRevRepository
     */
    protected $accordoRevRepository;

    /**
     * @var FasciaVitaRepository
     */
    protected $fasciaVitaRepository;

    /**
     * @var FasciaRamiPrefRepository
     */
    protected $fasciaRamiPrefRepository;

    /**
     * @var FasciaManager
     */
    protected $fasciaManager;

    /**
     * @var \metadigit\core\mail\Mailer
     */
    protected $mailer;

    /**
     * @var AgencyRepository
     */
    protected $agencyRepository;

    /**
     * @var UserRepository
     */
    protected $userRepository;

    /**
     * @param array $criteriaExp
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    public function synthesis(array $criteriaExp)
    {
        $totals   = [ "agencies" => 0, "plans" => 0 ];
        $statuses = [
            "DA_LAVORARE" => [ "label" => "DA LAVORARE", "plans" => 0, "onTotal" => 0, "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ],
            "IN_LAVORAZIONE" => [ "label" => "IN LAVORAZIONE", "plans" => 0, "onTotal" => 0, "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ],
            "DA_RILAVORARE" => [ "label" => "DA RILAVORARE", "plans" => 0, "onTotal" => 0, "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ],
            "ATTESA_RICALCOLO" => [ "label" => "DA RICALCOLARE", "plans" => 0, "onTotal" => 0, "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ],
            "SOTTOSCRITTO" => [ "label" => "SOTTOSCRITTI", "plans" => 0, "onTotal" => 0, "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ],
            "TOTALE" => [ "rpOneLevel" => 0, "rpTwoLevel" => 0, "lifeOneLevel" => 0, "lifeTwoLevel" => 0 ]
        ];

        $criteriaString = implode('|', $criteriaExp);
        $agreements   = $this->accordoRepository->fetchAll(null, null, null, $criteriaString);
        $agreementIds = [];
        foreach ($agreements as $a) {
            // Agencies total
            $totals["agencies"] += 1;
            // Plans total
            if ($a->partecipante) {
                $totals["plans"] += 1;
                // Agreement ids
                $agreementIds[] = $a->id;
                // Plans status
                $statuses[$a->status]["plans"] += 1;
            }
        }

        if ($totals["plans"] > 0) {
            $statuses["DA_LAVORARE"]["onTotal"] = round((($statuses["DA_LAVORARE"]["plans"] / $totals["plans"]) * 100), 2);
            $statuses["IN_LAVORAZIONE"]["onTotal"] = round((($statuses["IN_LAVORAZIONE"]["plans"] / $totals["plans"]) * 100), 2);
            $statuses["DA_RILAVORARE"]["onTotal"] = round((($statuses["DA_RILAVORARE"]["plans"] / $totals["plans"]) * 100), 2);
            $statuses["ATTESA_RICALCOLO"]["onTotal"] = round((($statuses["ATTESA_RICALCOLO"]["plans"] / $totals["plans"]) * 100), 2);
            $statuses["SOTTOSCRITTO"]["onTotal"] = round((($statuses["SOTTOSCRITTO"]["plans"] / $totals["plans"]) * 100), 2);
        }

        $statuses = $this->synthesisAgreementsRev($agreementIds, $statuses);

        return [
            $totals, array_values($statuses)
        ];
    }

    /**
     * Retrieve the data attached to the agreement's revisions
     * @param $agreementIds
     * @param $statuses
     * @return mixed
     * @throws \Exception
     */
    private function synthesisAgreementsRev($agreementIds, $statuses)
    {
        $implodeIds = implode(",", $agreementIds);
        $criteriaExp[] = "accordo_id,IN,$implodeIds";
        $criteriaExp[] = "isLatest,EQ,1";
        $criteriaString = implode('|', $criteriaExp);
        $agreementsRev = $this->accordoRevRepository->fetchAll(null, null, null, $criteriaString);
        foreach ($agreementsRev as $rev) {
            if (!key_exists($rev->status, $statuses)) {
                continue;
            }

            $statuses[$rev->status]["rpOneLevel"] += $rev->ramiPrefAnnL1IncassiCur;
            $statuses[$rev->status]["rpTwoLevel"] += $rev->ramiPrefAnnL2IncassiCur;
            $statuses[$rev->status]["lifeOneLevel"] += $rev->vitaAnnL1IncassiCur;
            $statuses[$rev->status]["lifeTwoLevel"] += $rev->vitaAnnL2IncassiCur;

            $statuses["TOTALE"]["rpOneLevel"] += $rev->ramiPrefAnnL1IncassiCur;
            $statuses["TOTALE"]["rpTwoLevel"] += $rev->ramiPrefAnnL2IncassiCur;
            $statuses["TOTALE"]["lifeOneLevel"] += $rev->vitaAnnL1IncassiCur;
            $statuses["TOTALE"]["lifeTwoLevel"] += $rev->vitaAnnL2IncassiCur;
        }

        return $statuses;
    }

    public static function deadline($soft = false)
    {
        return \DateTime::createFromFormat("Y-m-d", $soft ? static::$DEADLINE_SOFT : static::$DEADLINE_HARD);
    }

    public function isDeadlineExpired()
    {
        return new \DateTime() > static::deadline();
    }

    public function isAdmin()
    {
        return $_SESSION["AUTH"] && $_SESSION["AUTH"]["UTYPE"] == "AMMINISTRATORE";
    }

    public function isPersChanged(AccordoRev $revision)
    {
        $oldRevision = $this->accordoRevRepository->fetch($revision->id);
        $persValues  = [
            "vitaAnnL2IncassiCur",
            "vitaAnnL2ExtraRappel",
            "ramiPrefAnnL2Obj",
            "ramiPrefAnnL2Incremento",
            "ramiPrefAnnL2IncassiCur",
            "ramiPrefAnnL2Rappel",
            "ramiPrefAnnL2ObjPersonal",
            "ramiPrefAnnL2IncrementoPersonal",
            "ramiPrefAnnL2IncassiCurPersonal",
            "ramiPrefAnnL2RappelPersonal",
        ];

        foreach ($persValues as $field) {
            if ($revision->$field != $oldRevision->$field) {
                $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__, "$field has changed");
                return true;
            }
        }

        return false;
    }

    private function isRappelAllowed($aliquota, $rappel, $fascia, $takingType)
    {

        if ($_SESSION['AUTH']['UTYPE'] === "AMMINISTRATORE") {
            return true;
        }

        $rappels = $this->fasciaManager->getRappelsByFascia($fascia, $takingType);
        if (count($rappels) == 0) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, "No rappel per fascia:","{$fascia->name}");
            return false;
        }
        // n.b. basato sull'assunzione che i rappel della fascia siano già
        // ordinati per obj ascendente
        for ($i = 0; $i < self::$LIVELLI_PERSONALIZZAZIONE; $i++) {
            if ($aliquota <= $this->fasciaManager->getRappel($rappels, $i)->obj) {
                break;
            }
        }
        // Gestione aliquote di *incremento* oltre soglia massima:
        // se $i >= self::$LIVELLI_PERSONALIZZAZIONE non abbiamo trovato una fascia che "contiene"
        // l'aliquota stessa, quindi questa deve andare oltre i limiti impostati.
        // In tal caso il rappel consentito è il massimo rappel configurato,
        // ovvero quello della personalizzazione più alta: ricerchiamolo.
        if ($i >= self::$LIVELLI_PERSONALIZZAZIONE) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Aliquota oltre soglia massima => Fascia:{$fascia->name} aliquota:$aliquota");
            for ($k = self::$LIVELLI_PERSONALIZZAZIONE - 1; $k >= 0; $k--) {
                $objRappel = $this->fasciaManager->getRappel($rappels, $k);
                // @fixme
                // Non realmente da fixare, ma una nota da tenere a mente:
                // i livelli di personalizzazione sono tutti *esistenti* a livello di dati,
                // ma quelli non utilizzati hanno i valori pari a zero. Perciò, se ne troviamo
                // uno inferiore o uguale a zero, interpretiamo come "livello non utilizzato"
                if ($objRappel->rappelMax > 0)
                    break;
            }
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Rappel richiesto/massimo consentito: $rappel/{$objRappel->rappelMax}");
            return $objRappel!=null && $rappel <= $objRappel->rappelMax;
        }
        // Aliquota di incremento standard, cioè non oltre le soglie prestabilite:
        // banalmente controlliamo che il rappel richiesto sia inferiore di quello consentito.
        if ($i >= 0)
            return ($rappel <= $this->fasciaManager->getRappel($rappels, $i)->rappelMax);

        // Per tutto il resto... c'è false ;)
        return false;
    }

    public function assignFasciaVita($accordo, $revision)
    {
        $criteria[] = "year,EQ,$accordo->year";
        $criteria[] = "lowerBound,GTE,$revision->vitaAnnL1IncassiCur";
        $criteria[] = "upperBound,LTE,$revision->vitaAnnL1IncassiCur";
        $implode = implode(",", $criteria);
        $fasce   = $this->fasciaVitaRepository->fetchAll(null, null, null, $implode);
        $count   = count($fasce);

        if ($count == 1) {
            $this->accordoRepository->update($accordo->id, [ "fasciaVita" => $fasce[0]->name ]);
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Assegnata nuova fascia vita: {$fasce[0]->name}");
        } else if ($count > 1) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Warning: rilevate più fasce vita per valore $revision->vitaAnnL1IncassiCur");
        } else {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Warning: fascia vita non trovata per valore $revision->vitaAnnL1IncassiCur");
        }
    }

    protected function mailAfterDeadline($dm, $agenziaId) {
        try {
            $Message = $this->mailer->newMessage();
            $Message->setSubject("Aggiornamento accordo")
                ->setFrom(array("<EMAIL>" => "ROBOT PortaleAgendo"))
                ->setReplyTo("<EMAIL>")
                ->setReturnPath("<EMAIL>")
                ->setContentType("text/html");
            $Message->setBody("Gentile {$dm->nome} {$dm->cognome},<br/>l'accordo economico dell'agenzia $agenziaId è stato modificato da un amministratore. Può prenderne visione accedendo all'applicazione in Portale Agendo.<br/>Distinti saluti");
            switch(\metadigit\core\ENVIRONMENT) {
                case 'PROD': $Message->setTo(array($dm->email => $dm->email)); break;
                default: $Message->setTo(array("<EMAIL>" => "test"));
            }
            $this->mailer->send($Message);
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Email $dm->email after deadline.");
        } catch (\Exception $ex) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,$ex->getMessage());
            trigger_error("Email failed.");
        }
    }

    public function createRevision($revision, $mode = "APP")
    {
        if (($isExpired = $this->isDeadlineExpired()) && !$this->isAdmin()) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Deadline expired.");
            return false;
        } elseif ($isExpired) {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Deadline expired - Admin update.");
            $adminAfterDeadline = true;
        } else {
            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Regular update.");
            $adminAfterDeadline = false;
        }

        // Update data in revision
        $year                = YearManager::getYear();
        $revision->mode      = $mode;
        $revision->updatedAt = date(DATE_ISO8601);

        $revision->ramiPrefAnnL2ObjPersonal        = $revision->ramiPrefAnnL2ObjPersonal > 0 ? $revision->ramiPrefAnnL2ObjPersonal : null;
        $revision->ramiPrefAnnL2IncrementoPersonal = $revision->ramiPrefAnnL2IncrementoPersonal > 0 ? $revision->ramiPrefAnnL2IncrementoPersonal : null;
        $revision->ramiPrefAnnL2IncassiCurPersonal = $revision->ramiPrefAnnL2IncassiCurPersonal > 0 ? $revision->ramiPrefAnnL2IncassiCurPersonal : null;
        $revision->ramiPrefAnnL2RappelPersonal     = $revision->ramiPrefAnnL2RappelPersonal > 0 ? $revision->ramiPrefAnnL2RappelPersonal : null;
        $revision->vitaAnnL2Obj                    = $revision->vitaAnnL2Obj > 0 ? $revision->vitaAnnL2Obj : null;
        $revision->vitaAnnL2IncassiCur             = $revision->vitaAnnL2IncassiCur > 0 ? $revision->vitaAnnL2IncassiCur : null;
        $revision->vitaAnnL2ExtraRappel            = $revision->vitaAnnL2ExtraRappel > 0 ? $revision->vitaAnnL2ExtraRappel : null;

        $persRP = $persV = false;
        if ($revision->ramiPrefAnnL2ObjPersonal != null ||
            $revision->ramiPrefAnnL2IncrementoPersonal != null ||
            $revision->ramiPrefAnnL2IncassiCurPersonal != null ||
            $revision->ramiPrefAnnL2RappelPersonal != null) {
            $persRP = true;
        }
        if ($revision->vitaAnnL2Obj != null ||
            $revision->vitaAnnL2IncassiCur != null ||
            $revision->vitaAnnL2ExtraRappel != null) {
            $persV = true;
        }
        $accordo  = $this->accordoRepository->fetch($revision->accordo_id);
        $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Accordo");
        $fasciaV  = $this->fasciaVitaRepository->fetchOne(null, null, "name,EQ,$accordo->fasciaVita|year,EQ,$year");
        $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Fascia VIta");
        $fasciaRP = $this->fasciaRamiPrefRepository->fetchOne(null, null, "name,EQ,$accordo->fasciaRamiPref|year,EQ,$year");
        $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Fascia RP");
        $changed  = $this->isPersChanged($revision);
        if ($persRP && $persV) {
            $accordo->type = Accordo::$TYPE_PERSONALIZZATO_COMBO;
            if ($changed && !$this->isAdmin() && !self::isRappelAllowed($revision->vitaAnnL2Obj, $revision->vitaAnnL2ExtraRappel, $fasciaV, "vita")) {
                $this->trace(LOG_DEBUG, 1, __CLASS__, "Vita not allowed","");
                return false;
            }
            if ($changed && !$this->isAdmin() && !self::isRappelAllowed($revision->ramiPrefAnnL2ObjPersonal, $revision->ramiPrefAnnL2RappelPersonal, $fasciaRP, "rp")) {
                $this->trace(LOG_DEBUG, 1, __CLASS__, "RP not allowed","");
                return false;
            }
        } else if ($persRP) {
            $accordo->type = Accordo::$TYPE_PERSONALIZZATO_RAMIPREF;
            if ($changed && !$this->isAdmin() && !self::isRappelAllowed($revision->ramiPrefAnnL2ObjPersonal, $revision->ramiPrefAnnL2RappelPersonal, $fasciaRP, "rp")) {
                $this->trace(LOG_DEBUG, 1, __CLASS__, "RP not allowed","");
                return false;
            }
        } else if ($persV) {
            $accordo->type = Accordo::$TYPE_PERSONALIZZATO_VITA;
            if ($changed && !$this->isAdmin() && !self::isRappelAllowed($revision->vitaAnnL2Obj, $revision->vitaAnnL2ExtraRappel, $fasciaV, "vita")) {
                $this->trace(LOG_DEBUG, 1, __CLASS__, "Vita not allowed","");
                return false;
            }
        } else {
            $accordo->type = Accordo::$TYPE_STANDARD;
        }

        // Forzo lo status in "da rilavorare" se la modifica proviene da un Amministratore.
        if ($_SESSION['AUTH']['UTYPE'] == "AMMINISTRATORE") {
            $revision->status = Accordo::$STATUS_DA_RILAVORARE;
        }

        $year = YearManager::getYear();
        if (property_exists($revision, "status") && $revision->status != null) {
            if ($accordo->status != $revision->status && $revision->status == Accordo::$STATUS_DA_RILAVORARE) {
                $agenzia         = $this->agencyRepository->fetch($accordo->agenzia_id);
                $dm = $this->userRepository->fetchOne(null, null, "district,EQ,$agenzia->district|type,EQ,DISTRICTMGR");
                $messageSubject = "Accordo $year agenzia {$accordo->agenzia_id} ricalcolato";
                if (empty($dm)) {
                    throw new \Exception('District Manager not found for ' . $accordo->agenzia_id);
                }
                try {
                    $Message = $this->mailer->newMessage();
                    $Message->setSubject($messageSubject)
                        ->setFrom(array('<EMAIL>'=>'ROBOT PortaleAgendo'))
                        ->setReplyTo('<EMAIL>')
                        ->setReturnPath('<EMAIL>')
                        ->setContentType('text/html');
                    $Message->setBody("Gentile {$dm->nome} {$dm->cognome},<br/>l'accordo economico dell'agenzia {$accordo->agenzia_id} è stato ricalcolato e quindi può  accedere all’applicativo per effettuare le modifiche che ritiene opportune.<br/>Distinti saluti");
                    switch(\metadigit\core\ENVIRONMENT) {
                        case 'PROD': $Message->setTo(array($dm->email => $dm->email)); break;
                        default: $Message->setTo(array('<EMAIL>' => 'test'));
                    }
                    $this->mailer->send($Message);
                    $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Email '$messageSubject' sent.");
                } catch (\Exception $ex) {
                    $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,"Email '$messageSubject' failed.");
                    $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,$ex->getMessage());
                    trigger_error("Email failed.");
                }
            }

            $accordo->status = $revision->status;
            if ($accordo->status == Accordo::$STATUS_DA_RILAVORARE ||
                $accordo->status == Accordo::$STATUS_ATTESA_RICALCOLO ||
                $adminAfterDeadline) {
                //$revision->statusChangeMessage = trim($revision->statusChangeMessage);
            }
        }

        try {
            $this->pdoBeginTransaction();

            $revision->status = $accordo->status;
            $revArray = $revision->toArray();
            unset($revArray["id"]);
            $this->accordoRevRepository->insert(null, $revArray);
            $lastRevision = $this->accordoRevRepository->fetchOne(null, "id.DESC", "accordo_id,EQ,$accordo->id");
            $this->setLatestRev($lastRevision);
            $this->accordoRepository->update($accordo->id, $accordo->toArray());
            $this->assignFasciaVita($accordo, $lastRevision);

            $this->pdoCommit();

            if ($adminAfterDeadline) {
                $agenzia         = $this->agencyRepository->fetch($accordo->agenzia_id);
                $districtManager = $this->userRepository->fetchOne(null, null, "district,EQ,$agenzia->district|type,EQ,DISTRICTMGR");
                $this->mailAfterDeadline($districtManager, $accordo->agenzia_id);
            }

            return true;
        } catch (\Exception $ex) {
            $this->pdoRollBack();

            $this->trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__,$ex->getMessage());

            return false;
        }
    }

    public function setLatestRev($revision)
    {
        if ($result = $this->accordoRevRepository->resetLatest($revision)) {
            return $this->accordoRevRepository->update($revision->id, [ "isLatest" => 1 ]);
        }

        return false;
    }
}
