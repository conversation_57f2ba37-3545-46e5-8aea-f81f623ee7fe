<?php

namespace data\apps\formazione\Repositories;

use metadigit\core\Kernel;
use TrainingDev\Repositories\Interfaces\ClassRoomRepositoryInterface;

class ClassRoomRepository extends \metadigit\core\db\orm\Repository implements ClassRoomRepositoryInterface
{
    public function find($id)
    {
        return $this->fetch($id);
    }

    public function changeStatus($id, $model)
    {
        return $this->update($id, $model);
    }

    public function store(array $model)
    {
        return $this->insert(null, $model);
    }

    public function findByLocation($locationId)
    {
        return $this->fetchAll(null, null, null, 'location_id,EQ,'.$locationId);
    }

    public function findByType($type, $year = null)
    {
        $criteriaExp = "groupamaType,EQ,$type|status,EQ,active";
        if ($year) {
            $criteriaExp .= "year,EQ,$year";
        }

        return $this->fetchAll(null, null, null, $criteriaExp);
    }

    public function countCreditsByType($type, $year = null)
    {
        $filters = $params = [];

        $sql = "SELECT SUM(cl.credits) AS credits
                  FROM vw_tra_class AS cl
                WHERE cl.groupamaType = :type ";

        if ($year) {
            $filters[] = "(cl.year = :year)";

            $params[':year'] = $year;
        }

        if ($filters) {
            $sql .= " AND " . implode(" AND ", $filters);
        }

        $params[':type'] = $type;

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn(0);
    }

    public function completedCoursesByUserId($agencyCode, $userId)
    {
        $sql = "SELECT a.courseTitle, a.result, a.credits, a.course_id, a.class_id, a.id, a.result, a.groupamaType, a.year
                  FROM vw_tra_attendance AS a
                WHERE a.agencyCode = :agencyCode
                  AND CURDATE() >= a.firstDay
                  AND a.user_id = :userId
                  AND a.state = 'signedup'
                  AND a.courseStatus IN('on', 'arc', '')
                GROUP BY a.class_id
                ORDER BY a.firstDay DESC
                LIMIT 4";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([
            ':agencyCode' => $agencyCode,
            ':userId' => $userId
        ]);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findByCourse($courseId, $year = null, $inactive = null)
    {
        $criteriaExp = "course_id,EQ,$courseId";

        if (!$inactive) {
            $criteriaExp .= "|status,EQ,active";
        }

        if ($year) {
            $criteriaExp .= "|year,EQ,$year";
        }

        $orderExp = 'firstDay.ASC';

        return $this->fetchAll(null, null, $orderExp, $criteriaExp);
    }

    public function getVacantSeats($classroomId)
    {
        $sql = "SELECT CASE WHEN (cl.seats - COUNT(*)) < 0 THEN 0 ELSE (cl.seats - COUNT(*)) END AS seats
                  FROM tra_class cl JOIN tra_attendance a ON cl.id = a.class_id
                 WHERE cl.id = :classroomId";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([
            ':classroomId' => $classroomId
        ]);

        return $stmt->fetchColumn(0);
    }

    public function setLocationNull($locationId)
    {
        $sql = "UPDATE tra_class SET location_id = NULL WHERE location_id = :locationId";

        return Kernel::pdo()->prepare($sql)->execute([ ':locationId' => $locationId ]);
    }

    public function findBy(array $filters)
    {
        $criteriaExp = "";

        foreach ($filters as $key => $value) {
            $criteriaExp .= "{$key},EQ,{$value}|";
        }

        $criteriaExp = substr($criteriaExp, 0, -1);

        if (empty($criteriaExp)) {
            return null;
        }

        return $this->fetchOne(null, null, $criteriaExp);
    }

    /**
     * Remove classroom area
     *
     * @param $classId
     * @return bool
     */
    public function removeAreaClassroom($classId)
    {
        Kernel::pdo()->beginTransaction();

        try {
            // Remove attendances linked to the classroom
            $sql = "DELETE FROM tra_attendance WHERE class_id = :classId";
            Kernel::pdo()->prepare($sql)->execute([ ':classId' => $classId ]);
            // Remove classroom
            $this->delete($classId);

            Kernel::pdo()->commit();
        } catch (\Exception $exception) {
            Kernel::pdo()->rollBack();

            return false;
        }

        return true;
    }

    public function findByAgency($courseId, $agencyId)
    {
        $sql = "
                SELECT cl.firstDay, l.city, cl.id, cl.data
                    FROM tra_class cl
                    JOIN tra_location l ON cl.location_id = l.id
                    LEFT JOIN tra_attendance a ON cl.id = a.class_id
                    LEFT JOIN users u ON a.user_id = u.id
                    WHERE cl.course_id = :courseId AND u.agenzia_id = :agencyId
                GROUP BY cl.id
            ";

        $stmt = \metadigit\webconsole\Kernel::pdo()->prepare($sql);
        $stmt->execute([
            ':courseId' => $courseId,
            ':agencyId' => $agencyId
        ]);

        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

}
