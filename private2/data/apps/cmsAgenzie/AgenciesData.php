<?php namespace data\apps\cmsAgenzie;

use api\utils\ConvertToJson,
    data\apps\formazione\Models\Traits\GetPropertyFromData;

/**
 * @orm(source="vw_cms_agenzie_data", target="cms_agenzie_data")
 */
class AgenciesData
{
    use \metadigit\core\db\orm\EntityTrait, ConvertToJson, GetPropertyFromData;

    /**
     * @orm(primarykey)
     */
    protected $agenzia_id;

    /**
     * @orm(type="integer")
     */
    protected $approved;

    /**
     * @orm
     */
    protected $agencyEntrancePhoto;

    /**
     * @orm
     */
    protected $whatsapp;

    /**
     * @orm
     */
    protected $pec;

    /**
     * @orm
     */
    protected $url;

    /**
     * @orm
     */
    protected $text;

    /**
     * @orm
     */
    protected $description;

    /**
     * @orm(type="boolean")
     */
    protected $showWeeklySchedule;

    /**
     * @orm(readonly)
     */
    protected $area;

    /**
     * @orm(readonly)
     */
    protected $distretto;

    /**
     * @orm(readonly)
     */
    protected $nome;

    /**
     * @orm(readonly)
     */
    protected $localita;

    /**
     * @orm(readonly)
     */
    protected $groupama_id;

    /**
     * @orm(readonly)
     */
    protected $link;

    /**
     * @orm(readonly)
     */
    protected $shortLink;

    /**
     * @return mixed
     */
    public function getAgenziaId()
    {
        return $this->agenzia_id;
    }

    /**
     * @param mixed $agenzia_id
     */
    public function setAgenziaId($agenzia_id)
    {
        $this->agenzia_id = $agenzia_id;
    }

    /**
     * @return mixed
     */
    public function getAgencyEntrancePhoto()
    {
        return $this->agencyEntrancePhoto;
    }

    /**
     * @param mixed $agencyEntrancePhoto
     */
    public function setAgencyEntrancePhoto($agencyEntrancePhoto)
    {
        $this->agencyEntrancePhoto = $agencyEntrancePhoto;
    }

    /**
     * @return mixed
     */
    public function getapproved()
    {
        return $this->approved;
    }

    /**
     * @param mixed $approved
     */
    public function setapproved($approved)
    {
        $this->approved = $approved;
    }

    /**
     * @return mixed
     */
    public function getUrl()
    {
        return $this->url;
    }

    /**
     * @param mixed $url
     */
    public function setUrl($url)
    {
        $this->url = $url;
    }

    /**
     * @return mixed
     */
    public function getText()
    {
        return $this->text;
    }

    /**
     * @param mixed $text
     */
    public function setText($text)
    {
        $this->text = $text;
    }

    /**
     * @return mixed
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * @param mixed $area
     */
    public function setArea($area)
    {
        $this->area = $area;
    }

    /**
     * @return mixed
     */
    public function getDistretto()
    {
        return $this->distretto;
    }

    /**
     * @param mixed $distretto
     */
    public function setDistretto($distretto)
    {
        $this->distretto = $distretto;
    }

    /**
     * @return mixed
     */
    public function getwhatsapp()
    {
        return $this->whatsapp;
    }

    /**
     * @param mixed $whatsapp
     */
    public function setwhatsapp($whatsapp)
    {
        $this->whatsapp = $whatsapp;
    }

    /**
     * @return mixed
     */
    public function getPec()
    {
        return $this->pec;
    }

    /**
     * @param mixed $pec
     */
    public function setPec($pec)
    {
        $this->pec = $pec;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

}
