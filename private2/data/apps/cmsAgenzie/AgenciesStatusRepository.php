<?php

namespace data\apps\cmsAgenzie;

use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;

class AgenciesStatusRepository extends Repository
{
    public function changeAgencyStatus($agency_id, $status)
    {
        $pdo = Kernel::pdo();
        $query = "UPDATE cms_agenzie_status SET approved = $status, standard = 0, user_id = '{$_SESSION["AUTH"]["UID"]}' where agenzia_id = '$agency_id'";

        $data = $pdo->query($query)->execute();

        return $data;

    }
}
