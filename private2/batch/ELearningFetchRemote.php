<?php

namespace batch;


use data\apps\formazione\Managers\ELearningManager;
use data\apps\formazione\Models\Attendance;
use data\apps\formazione\Models\ELearningFetchStatus;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response;
use service\AbiService;

class ELearningFetchRemote
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
	use \metadigit\core\db\PdoTrait;
	use \metadigit\lib\batch\BatchTrait;
	use \metadigit\core\CoreTrait;

    const CORSI_ANTIRICICLAGGIO = ['e1185c40', 'ANANT', 'ANTIRICI_ANIASAFE_NOV24'];
    const CORSI_GDPR = ['GDPR_RETE_AG', 'GDPR_RETE_CL'];

    /**
     * Endpoint URL
     */
    public $endpoint;

    /**
     * Token
     */
    public $accessToken;

    /**
     * Data iniziale di default
     * @var string
     */
    public $fetchDateStart;

    /**
     * Intervallo di acquisizione, in giorni
     * @var int
     */
    public $fetchDaysInterval;
    /**
     * TRUE:  calcola le date ma non invia
     * FALSE: procede normalmente
     * @var bool
     */
    protected $pretend;

    /**
     * Log vars
     */
    protected $logXml;
    protected $logDirectory;

    /**
     * Blocca l'acquisizione all'anno impostato.
     * @var int
     */
    protected $lockYear;

    /**
     * @var array
     * 	[0]: DateTime - Data iniziale di acquisizione, computata
     * 	[1]: DateTime - Data finale di acquisizione, computata
     */
    protected $dateInterval;


    /**
     * Retrieve the data from the E-Learning Platform
     *
     * @batch(description="Formazione: recupero dei dati dalla piattaforma e-learning")
     * @throws \Exception
     */
    public function runAction(Request $Req, Response $Res)
    {
        $matches = array();
        $this->dateInterval = array();
        $this->config();

        // Init date
        $this->fetchDateStart = new \DateTime($this->fetchDateStart);

        // Init log
        $this->log('Setup service');
//        $this->log('Endpoint ' . $this->endpoint);
//        $this->log('Token ' . $this->accessToken);

        // Determina gli intervalli di lettura
        $this->dateInterval = $interval = $this->getNextInterval($this->fetchDaysInterval);
        if (empty($this->dateInterval)) {
            $this->log($this->dateError);
            return;
        }
        $this->log("Start fetch interval: " . $interval[0]->format(DATE_ISO8601));
        $this->log("Start end interval  : " . $interval[1]->format(DATE_ISO8601));

        // Init Manager
        $serviceManager = new ELearningManager();
        $abiService = new AbiService();
//        $serviceManager->endpoint = $this->endpoint;
//        $serviceManager->accessToken = $this->accessToken;

        // Init status
        $fetchStatus = new ELearningFetchStatus();
        $fetchStatus->date = date(DATE_ISO8601);
        $fetchStatus->dateInterval0 = $this->dateInterval[0]->format(DATE_ISO8601);
        $fetchStatus->dateInterval1 = $this->dateInterval[1]->format(DATE_ISO8601);
        $fetchStatus->success = FALSE;
        $fetchStatus->message = '';

        try {
            // Invocazione e controllo della response
            // $response = $serviceManager->fetchByInterval($interval[0], $interval[1]);
            $from = $interval[0]->format('Ymd');
            $to = $interval[1]->format('Ymd');
            $response = $abiService->partecipationReport($from, $to);
            if (!$response) {
                $fetchStatus->message = 'Network error, remote read failed';
                throw new \Exception('Network error, remote read failed');
            }
            // Parse della response.
            $result = $serviceManager->parseResponse($response);

            $countSubscriptions = 0;
            $countCorsi = 0;

            if ($this->pretend) {
                $this->log("Exit because of pretend=TRUE.");
                $this->log(print_r($result, TRUE));
                return;
            }

            // Init transazione
            $this->pdoBeginTransaction();

            $matches = [
                'no' => [],
                'dupes' => [],
                'neo' => [],
                'ok' => []
            ];

            // Iterazioni di scrittura.
            foreach ($result as $id => $remoteCorso) {
                $this->log("Remote Course ID ". $id);

                if (!in_array($remoteCorso['instance']->tipo, [ 'NORM', 'SSPR', 'TECN', 'GIUR', 'TECA', 'ADMG', 'INFO', 'ACOP' ])) {
                    throw new \Exception("Tipo Corso is not valid!");
                }

                if (!$localCorso = $this->getCourse($id, $this->getYear())) {
                    $res = $this->createCourse($id, $remoteCorso);
                    if (! $res) {
                        continue;
                    }

                    $localCorso = $this->getCourse($id, $this->getYear());
                }
                $abstractId = $localCorso['id'];
                $countIterationSubscriptions = 0;
                foreach ($remoteCorso['users'] as $subscription) {
                    $user = $this->getUserAuth($subscription['id_utente_agendo']);
                    if (empty($user)) {
                        //	@fixme
                        //	Per ora interrompo completamente il ciclo in caso di
                        //	errore. Se non lo facessi dovrei tenere traccia degli utenti
                        //	che non sono andati a buon fine, per poterli recuperare in
                        //	un secondo momento. Inoltre, questo ipotetico recupero dovrebbe essere
                        //	invocato esplicitamente sull'utente: se marcassi
                        //	questa esecuzione batch con success=1 questo intervallo di date
                        //	sarebbe interpretato come acquisito.

                        //	@update
                        //	La nuova direttiva è di saltare gli utenti non riconosciuti
                        //	e comunicarli per interventi di risoluzione manuale.
                        //	Eccezione disabilitata. throw new \Exception("Operation aborted since user {$subscription['id_utente_agendo']} doesnt exists.");

                        $this->log("User {$subscription['id_utente_agendo']} doesn't match.");
                        $matches['no'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];
                        continue;
                    }

                    if (!$user = $this->dealWithDuplicates($user, $subscription['id_utente_agendo'])) {
                        $matches['dupes'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];
                        continue;
                    }

//                    Con WEMOLE non saltiamo più i NEO (10/11/2023)
//
//                    $userData = $this->getUser($user['id']);
//                    if ($userData && $userData['type'] == 'INTERMEDIARIO' && $userData['ruolo'] == 'NEO') {
//                        $this->log("Skip NEO {$subscription['id_utente_agendo']}");
//                        $matches['neo'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];
//                        continue;
//                    }

                    // Aggiorno l'array di reportistica
                    $matches['ok'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];

                    // Update 30/10/2018: im alcuni casi sembra che dal sistema
                    // remoto vengano acquisite partecipazioni multiple di un
                    // utente ad uno stesso corso. Facciamo un check di sicurezza
                    // ed eventualmente saltiamo per evitare di interrompere
                    // l'intera procedura per un vincolo FK saltato.
                    if ($checkPartecipazione = $this->getAttendance($abstractId, $user['id'])) {
                        $this->log("Skip DUPLICATE corso $abstractId user: {$user['id']} {$subscription['id_utente_agendo']}");
                        continue;
                    }

                    if (! $classroomId = $this->getClassroom($abstractId)['id']) {
                        $this->createClassroom($abstractId, $user['id']);
                        $classroomId = $this->pdoLastInsertId();
                    }

                    $abstractPartecipazione = new Attendance();
                    $abstractPartecipazione->setUserId($user['id']);
                    $abstractPartecipazione->setCourseId($abstractId);
                    $abstractPartecipazione->setClassId($classroomId);
                    $abstractPartecipazione->setResult('ok');
                    $abstractPartecipazione->setCredits($localCorso['credits']);
                    $abstractPartecipazione->setRole($this->setRole($user));
                    $abstractPartecipazione->setScore($subscription['punteggio']);
                    $abstractPartecipazione->setCompletedAt($subscription['dataCompletamento']);
                    $abstractPartecipazione->setCreatedAt((new \DateTime('now'))->format('Y-m-d H:i:s'));
                    $abstractPartecipazione->setUpdatedAt((new \DateTime('now'))->format('Y-m-d H:i:s'));
                    $abstractPartecipazione->setState('signedup');
                    $abstractPartecipazione->setAuthorId(null);
                    $abstractPartecipazione->setElearningProvider('WEMOLE');

                    $this->createAttendance($abstractPartecipazione);

                    $countSubscriptions++;
                    $countIterationSubscriptions++;
                }
                if ($countIterationSubscriptions) {
                    $countCorsi++;
                }
            }
            // Stats
            $matchesOk = count($matches['ok']);
            $matchesNo = count($matches['no']);
            $countUsers = $matchesOk + $matchesNo;

            // Set status
            $fetchStatus->count = $countSubscriptions;
            $fetchStatus->success = TRUE;
            $fetchStatus->message .= "$matchesNo fails.";

            // Separo la logica della commit dati da quella
            // della scrittura dello status.
            $this->pdoCommit();
        } catch (\Exception $ex) {
            $this->trace(LOG_DEBUG, 1, 'TRY CATCH', __FUNCTION__, $ex->getMessage());

            $fetchStatus->success = FALSE;
            $this->log('Exception ' . $ex->getMessage());
            $this->pdoRollback();
            trigger_error($ex->getMessage());
        }

        $this->log("=============================");
        $this->log("Corsi acquisiti: $countCorsi");
        $this->log("Partecipazioni acquisite: $countSubscriptions");
        $this->log("Utenti: $countUsers");
        $this->log("Utenti matchati: $matchesOk (" . (number_format(($countUsers !== 0) ? $matchesOk/$countUsers*100 : 0, 2)) . "%)");
        $this->log("Utenti non matchati: $matchesNo (" . (number_format(($countUsers !== 0) ? $matchesNo/$countUsers*100 : 0, 2)) . "%)");
        $this->log("Utenti matchati duplicati non risolvibili: " . count($matches['dupes']));
        $this->log("Dettaglio utenti matchati duplicati non risolvibili: " . implode(", ", $matches['dupes']));

        // } Finally { :p
        //	@todo Log response XML in caso di errori.
        $this->saveFetchStatus($fetchStatus);

        // Delego la gestione dei match.
//$this->emailErrors($countSubscriptions, $matches, $response);
    }

    private function config()
    {
        // Service config
        $this->endpoint = 'https://[da-definire]/webservice/rest/server.php{report_partecipazione}';
        $this->accessToken = 'zakkwylde';
        $this->fetchDateStart = '2024-01-01T00:00+00:00';
        $this->fetchDaysInterval = '30';
        $this->pretend = false;

        // Local config
        $this->logXml = TRUE;
        $this->logDirectory = '/storage/portaleagendo.it/data/upload';

        // Locko le acquisizioni sull'anno hard-coded
        // per evitare accavallamenti.
        $this->lockYear = 2025;
    }

    /**
     *	Calcola il prossimo intervallo di lettura dati.
     *	Se non esiste nessuna sessione pregressa utilizza come base
     *	di partenza la proprietà $this->fetchDaysInterval, altrimenti
     *	il dateInterval1 dell'ultima sessione andata a buon fine.
     *
     *	@param int $timespan
     *		Intervallo di lettura espresso in giorni
     *	@return array
     *		Se la selezione va a buon fine restituisce array:
     *			0: data iniziale (DateTime)
     *			1: data finale (DateTime)
     *		altrimenti array vuoto.
     */
    protected function getNextInterval($timespan)
    {
        // Leggo la data di partenza.
        $status = $this->getStartDate();

        if (empty($status)) {
            $this->log('Fetch status not found, starting from fetchDateStart');
            $dateStart = clone $this->fetchDateStart;
        } else {
            $dateStart = new \DateTime($status[0]['dateInterval1']);
            $dateStart->add(new \DateInterval('PT1S'));
        }

        // Se la data di partenza è maggiore della data odierna
        // devo interrompere e rimandare l'acquisizione.
        if ($dateStart > new \DateTime(('now'))) {
            $this->dateError = "La data di partenza è superiore alla data odierna, acquisizione interrotta.";
            return array();
        }

        // Se la data di partenza non cade nell'anno attuale
        // devo interrompere e rimandare l'acquisizione.
        if ($dateStart->format('Y') != $this->lockYear) {
            $this->dateError = "La data di partenza non coincide con l'anno impostato ({$this->lockYear}), acquisizione interrotta.";
            return array();
        }

        $dateEnd = clone $dateStart;
        $offset = "P{$timespan}DT23H59M59S";
        $dateEnd = $dateEnd->add(new \DateInterval($offset));
        if ($dateStart->format('Y') != $dateEnd->format('Y')) {
            $old = $dateEnd->format('d/m/Y');
            $dateEnd = new \DateTime($dateStart->format('Y') . '-12-31T23:59:59+00:00');
            $this->log("Date end corrected from $old to " . $dateEnd->format('d/m/Y'));
        }

        // Se la data di fine è maggiore della data odierna
        // la sovrascrivo con DataOdierna - 1D.
        if ($dateEnd > $now = new \DateTime(('now'))) {
            $dateEnd = new \DateTime($now->format('Y') . '-' .
                $now->format('m') . '-' .
                $now->format('d') .
                'T23:59:59+00:00');
            $dateEnd->sub(new \DateInterval('P1D'));
            $this->log("La data di fine è superiore alla data odierna, data corretta in " . $dateEnd->format('Y-m-d H:i:s'));
        }

        return array ($dateStart, $dateEnd);
    }

    protected function dealWithDuplicates($users, $userId)
    {
        if (count($users) == 1) {
            return $users[0];
        }

        $this->log("Analyze matches for {$userId}...");

        $result = [];
        foreach ($users as $user) {
            if ($user['active'] && $user['agenzia_id'] && $user['type']) {
                $result[] = $user;
                continue;
            }

            $this->log("{$userId} is invalid");
        }

        $count = count($result);

        if ($count == 1) {
            $this->log("{$userId} resolved to ".$result[0]['id']);
            return $result[0];
        } elseif ($count == 0 || $count > 1) {
            $this->log("{$userId} ".$result[0]['id']." cannot be resolved");
        }

        return null;
    }

    /**
     * @return int
     * L'anno dell'acquisizione corrente, o NULL se l'intervallo
     * di acquisizione (dateInterval) non è stato valorizzato
     * propriamente.
     */
    protected function getYear()
    {
        if (empty($this->dateInterval) || empty($this->dateInterval[1])) {
            return NULL;
        }
        return $this->dateInterval[1]->format('Y');
    }

    private function getOne($query)
    {
        $result = $this->pdoQuery($query)->fetch();

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    private function getAll($query)
    {
        $result = $this->pdoQuery($query)->fetchAll();

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    private function getCourse($remoteId, $year)
    {
        $query = "SELECT * FROM tra_course c WHERE c.remoteId = '$remoteId' AND c.year = '$year'";
        return $this->getOne($query);
    }

    private function getUserAuth($id)
    {
        $query = "SELECT * FROM vw_users_auth WHERE id = '".$id."'";
        return $this->getAll($query);
    }

    private function getUser($id)
    {
        $query = "SELECT * FROM users WHERE id = '".$id."'";
        return $this->getOne($query);
    }

    private function createCourse($remoteId, $remoteCorso)
    {

        $isGdpr = 0;
        $isAnti = 0;

        if ( in_array($remoteId, self::CORSI_GDPR) ) {
            $isGdpr = 1;
        }

        if ( in_array($remoteId, self::CORSI_ANTIRICICLAGGIO) ) {
            $isAnti = 1;
        }

        if (!$remoteCorso['instance']->year) {
            return null;
        }

        try {
            $query = "INSERT INTO tra_course ( title, code, data, type, credits, groupamaType, tipo, status, year, filters, remoteId, gdpr, antiriciclaggio, modalita, erogato )
                  VALUES (:title, :code, :data, :type, :credits, :groupamaType, :tipo, :status, :year, :filters, :remoteId, :gdpr, :antiriciclaggio, :modalita, :erogato)";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'title' => $remoteCorso['instance']->title,
                'code' => $remoteId,
                'data' => '',
                'type' => 'register',
                'credits' => $remoteCorso['instance']->credits,
                'groupamaType' => 'e-learning',
                'tipo' => $remoteCorso['instance']->tipo,
                'status' => 'on',
                'year' => $remoteCorso['instance']->year,
                'filters' => null,
                'remoteId' => $remoteId,
                'gdpr' => $isGdpr,
                'antiriciclaggio' => $isAnti,
                'modalita' => 'DIST',
                'erogato' => 'DIREZ'
            ]);

            $this->log("Nuovo corso inserito. Codice remoto: ".$remoteId);
            $this->log("Crediti assegnati: ".$remoteCorso['instance']->credits);

            return $result;
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());

            return null;
        }
    }

    private function getStartDate()
    {
        $query = "SELECT * FROM tra_elearning_fetch_session WHERE success = 1 ORDER BY dateInterval1 DESC";
        return $this->getAll($query);
    }

    protected function logXmlResponse($xml)
    {
        if (!$this->logXml) {
            return;
        }
        $xmlfile = $this->getXmlFilename();
        file_put_contents($xmlfile, $xml);
    }

    protected function getXmlFilename()
    {
        return $this->logDirectory . "/elearning_".date('Y_m_d_H-i').".xml";
    }

    private function getAttendance($courseId, $userId)
    {
        $query = "SELECT * FROM vw_tra_attendance WHERE course_id = '".$courseId."' AND user_id = '".$userId."'";
        return $this->getOne($query);
    }

    private function createAttendance(Attendance $attendance)
    {
        try {
            $query = "INSERT INTO `tra_attendance` ( `user_id`, `course_id`, `class_id`, `result`, `data`, `role`, `createdAt`, `updatedAt`, `state`, `author_id`, `score`,`completedAt`,`credits`, `elearningProvider`)
                  VALUES (:userId, :courseId, :classId, :result, null, :role, :createdAt, :updatedAt, :state, :authorId, :score, :completedAt, :credits, 'WEMOLE')";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'userId' => $attendance->getUserId(),
                'courseId' => $attendance->getCourseId(),
                'classId' => $attendance->getClassId(),
                'result' => $attendance->getResult(),
                'role' => $attendance->getRole(),
                'createdAt' => $attendance->getCreatedAt(),
                'updatedAt' => $attendance->getUpdatedAt(),
                'state' => $attendance->getState(),
                'authorId' => $attendance->getAuthorId(),
                'score' => $attendance->getScore(),
                'completedAt' => $attendance->getCompletedAt(),
                'credits' => $attendance->getCredits()
            ]);

            $this->log("Nuova partecipazione inserita.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'createAttendance', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function saveFetchStatus($fetchStatus)
    {
        try {
            $query = "INSERT INTO `tra_elearning_fetch_session` ( `date`, `dateInterval0`, `dateInterval1`, `count`, `success`, `message`, `elearningProvider` )
                  VALUES (:date, :dateInterval0, :dateInterval1, :count, :success, :message, 'WEMOLE')";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'date' => $fetchStatus->date,
                'dateInterval0' => $fetchStatus->dateInterval0,
                'dateInterval1' => $fetchStatus->dateInterval1,
                'count' => $fetchStatus->count,
                'success' => $fetchStatus->success,
                'message' => $fetchStatus->message,
            ]);

            $this->log("Fetch status salvato.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'saveFetchStatus', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function getClassroom($courseId)
    {
        $query = "SELECT * FROM tra_class WHERE course_id = '".$courseId."' AND status = 'active'";
        return $this->getOne($query);
    }

    private function createClassroom($courseId)
    {
        try {
            $query = "INSERT INTO `tra_class` ( `course_id`, `status` )
                  VALUES (:course_id, :status)";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'course_id' => $courseId,
                'status' => 'active'
            ]);

            $this->log("Nuova classe salvata.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'createClassroom', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function setRole($user)
    {
        if ($user['type'] == 'AGENTE') {
            return $user['type'];
        }

        if ($user['type'] == 'INTERMEDIARIO' && $user['ruolo'] == 'NEO') {
            return $user['ruolo'];
        }

        if ($user['type'] == 'INTERMEDIARIO' && $user['ruolo'] != 'NEO') {
            return $user['type'];
        }
    }
}
