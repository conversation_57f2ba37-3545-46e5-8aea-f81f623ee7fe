<?php namespace api\ga;
use const metadigit\core\ENVIRONMENT;
use metadigit\core\db\orm\Repository,
	metadigit\core\http\Request,
	metadigit\core\http\Response,
	metadigit\core\web\controller\ActionController;

class Controller extends ActionController {

	/** @var \metadigit\core\db\orm\Repository */
	protected $IncentivazioniRepository;

	/**
	 * @param Request $Req
	 * @param Response $Res
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function bannersAction(Request $Req, Response $Res) {
		$hostname = (ENVIRONMENT=='PROD') ? 'intranet-prod.groupama.it':'intranet-test.groupama.it';

		// In primo piano
		$response = [

            // Focus fase2
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-focus2024-fase2.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/focus-2024-fase2/',
            ],

            // Supernova RF
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-supernova2-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/supernova-rf-2024/',
            ],

            // Arag
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-arag-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/arag-2024/',
            ],

            // Full Benessere
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-fullbenessere-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/fullbenessere-2024/',
            ],

            // Full Protection
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-fullprotection-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/fullprotection-2024/',
            ],

            // Supernova
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-supernova-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/supernova-2024/',
            ],

            // Focus
            [
                'img' => 'https://'.$hostname.'/staticportaleagendo/assets/dashboard/banner-focus-2024.jpg',
                'link' => 'https://'.$hostname.'/portaleagendo/focus-2024/',
            ],

		];

		// Incentivazioni attive
		$criteriaExp = 'id,GTE,52|id,!IN,91,153,154,155,156,164|statusAGT,EQ,ON|dataFine,GTE,'.date('Y-m-d');
		$data = $this->IncentivazioniRepository->fetchAll(1, null, 'dataInizio.DESC|id.DESC', $criteriaExp, Repository::FETCH_JSON, 'dashboard');
		foreach ($data as $incentiv) {
			array_unshift($response, [
				'img' => 'https://'.$hostname.'/staticportaleagendo/img/incentivazioni/'.$incentiv['id'].'-banner.png',
                'link' => 'https://'.$hostname.'/portaleagendo/#/incentivazioni/'.$incentiv['id'].'-'.$incentiv['url'].'/',
			]);
		}

		$Res->set('data', $response)->setView('json:');
	}
}
