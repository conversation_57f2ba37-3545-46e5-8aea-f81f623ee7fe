<?php namespace api\apps\incentive;

use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class IncentiveController extends \metadigit\core\web\controller\ActionController {

    /**
     * @var Repository
     */
    protected $incentive;

    /**
     * @var Repository
     */
    protected $incentiveData;

    /**
     * @var Repository
     */
    protected $restart;

    /**
     * @var Repository
     */
    protected $besmart;

    protected $config = [284, 283, 289, 290, 291, 293, 294, 295, 300, 304, 305, 311, 324, 325, 326, 329, 331, 333, 337];
    protected $allowedWelfare = ["G030", "G031", "G045", "G051", "G059", "G065", "G067", "G075", "G081", "G101", "G114", "G116", "G117", "G119", "G122", "G126", "G143", "G146", "G159", "G162", "G163", "G174", "G187", "G190", "G193", "G203", "G211", "G243", "G244", "G249", "G254", "G256", "G260", "G269", "G277", "G295", "G296", "G306", "G371", "G375", "G411", "G428", "G431", "G435", "G451", "G454", "G471", "G476", "G482", "G484", "G486", "G542", "G766", "G816", "G837", "G848", "G850", "G857", "G864", "G875", "G902", "G943", "G980", "G981", "G983", "G993", "GL23", "GL60", "GL63", "GL85", "GM12", "GM15", "GM19", "GM21", "GM23", "GM28", "GM40", "GM43", "GM51", "GM53", "GM63", "M18", "M35", "N033", "N036", "N048", "N050", "N079", "N086", "N144", "N173", "N180", "N268", "N312", "N335", "N392", "N465", "N479", "N569", "N852", "N863", "N959", "N984", "N992", "NB36", "NC60", "NC70", "ND16", "ND66", "ND70", "NF22", "NF54"];

    /**
     * Return data.
     *
     * @routing(method="GET", pattern="/")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function indexAction(Request $Req, Response $Res)
    {
        // @TODO escludere agenzie non partecipanti

        if (! in_array($_SESSION['AUTH']['UTYPE'], ['KA', 'AMMINISTRATORE', 'AGENTE', 'AREAMGR', 'DISTRICTMGR'])) {
            return http_response_code(401);
        }

        if (! $year = $Req->get('year')) {
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        $data = $this->incentive->fetchAll(
            null,
            null,
            "id",
            "year,EQ,$year",
            Repository::FETCH_ARRAY
        );

        $incentives = $this->format($data);

        if ($year == 2022) {
            $restart = $this->format($this->restart());
            $besmart = $this->format($this->besmart());
            $result = array_merge($restart, $besmart, $incentives);
            return $Res->set('data', array_values($result))->setView('json:');
        }

        return $Res->set('data', array_values($incentives))->setView('json:');
    }

    public function restart()
    {
        $data = $this->restart->fetchAll(
            null,
            null,
            "id",
            null,
            Repository::FETCH_ARRAY
        );

        return array_map(function($item){
            $item['program'] = 'restart';
            $item['protection'] = 6;
            return $item;
        }, $data);
    }

    public function besmart()
    {
        $data = $this->besmart->fetchAll(
            null,
            null,
            "id",
            null,
            Repository::FETCH_ARRAY
        );

        return array_map(function($item){
            $item['program'] = 'besmart';
            $item['protection'] = 7;
            return $item;
        }, $data);
    }

    protected function format($data)
    {
        foreach($data as $row) {
            if (! isset($result[$row['program']])) {
                $result[$row['program']] = [
                    'program' => $row['program'],
                    'status' => null,
                    'protection' => $row['protection'],
                    'start' => $row['start'],
                    'end' => $row['end'],
                    'lastUpdate' => $row['lastUpdateLabel'],
                    'id' => $row['id'],
                    'hasStatusDownload' => $this->checkIncentiveHasDownload($row['id']),
                ];
            }

            $result[$row['program']]['status'] = in_array($row['status'], ['inactive', 'active', 'started']);

            if ($row['lastUpdateLabel'] > $result[$row['program']]['lastUpdate']) {
                $result[$row['program']]['lastUpdate'] = $row['lastUpdateLabel'];
            }

            if ($row['start'] < $result[$row['program']]['start']) {
                $result[$row['program']]['start'] = $row['start'];
            }

            if ($row['end'] > $result[$row['program']]['end']) {
                $result[$row['program']]['end'] = $row['end'];
            }

            $result[$row['program']]['periods'][] = $row;

            if ( $row['id'] == 339 && $_SESSION['AUTH']['UTYPE'] === 'AGENTE' ) {
                if (! $this->isAllowedWelfare($row)) {
                    unset($result[$row['program']]);
                }
            }

        }

        return $result;
    }

    protected function checkIncentiveHasDownload($id)
    {
        return in_array($id, $this->config);
    }

    /**
     * Get relevant policies on specific incentive
     * @routing(pattern="/policies-detail/<incentive_id>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $incentive_id
     * @throws \metadigit\core\context\ContextException
     */
    public function policiesDetailDownloadAction(Request $Req, Response $Res, $incentive_id)
    {

        if (! $agenzia_id = $_SESSION['AUTH']['AGENZIA']) {
            return http_response_code(400);
        }

        try {

            $incentive = $this->incentive->fetch($incentive_id, Repository::FETCH_ARRAY);
            $data = $this->incentiveData->fetchAll(null, null, null, "incentive_id,EQ,$incentive_id|agenzia_id,EQ,$agenzia_id", Repository::FETCH_ARRAY);

            return $Res
                ->set('data', $data)
                ->set('saveName', strtoupper($incentive['program']) . " - Dettaglio Polizze $agenzia_id")
                ->setView('file-excel:/policies-detail');
        } catch(\Exception $Ex) {
            http_response_code(500);
            TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
        }

    }

    protected function isAllowedWelfare()
    {
        $agencyId = $_SESSION['AUTH']['AGENZIA'];
        if (in_array($agencyId, $this->allowedWelfare)) {
            return true;
        }

        return false;
    }

}
