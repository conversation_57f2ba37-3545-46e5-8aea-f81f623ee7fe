<?php

namespace api\apps\cmsAgenzie;

use api\utils\Debugger;
use api\utils\MakePaginationData;
use data\apps\cmsAgenzie\AgenciesDataRepository;
use data\apps\cmsAgenzie\AgenciesStatusRepository;
use data\apps\cmsAgenzie\DayScheduleRepository;
use data\apps\cmsAgenzie\EmployeesRepository;
use data\apps\cmsAgenzie\EmployeeMailsRepository;
use data\apps\cmsAgenzie\FileManager;
use data\apps\cmsAgenzie\Utils;
use data\UsersRepository;
use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use data\apps\formazione\Utils\Json;
use metadigit\core\CoreTrait;
use metadigit\core\db\PdoTrait;

use const metadigit\core\ASSETS_DIR;
use const metadigit\core\ENVIRONMENT;

class BackendController extends \metadigit\core\web\controller\ActionController
{
    use PdoTrait, CoreTrait, Debugger;

    use MakePaginationData, \metadigit\core\db\PdoTrait;

    const MAIL_DELAY_TIME = "-4 hours";
    const MAIL_SUBJECT = "Mostra il tuo profilo in MyPage, accedi per accettare l'informativa";
    const MAIL_BODY = (__DIR__ . "/tpl/privacyPolicyMail.php");

    /**
     * @var \metadigit\core\mail\Mailer
     */
    protected $mailer;

    /**
     * @var Repository
     */
    protected $agencies;

    /**
     * @var AgenciesDataRepository
     */
    protected $agenciesData;

    /**
     * @var AgenciesStatusRepository
     */
    protected $agenciesStatus;

    /**
     * @var EmployeesRepository
     */
    protected $employees;

    /**
     * @var EmployeeMailsRepository
     */
    protected $employeeMails;

    /**
     * @var DayScheduleRepository
     */
    protected $schedules;

    /**
     * @var UsersRepository
     */
    protected $users;

    /**
     * @var Utils
     */
    protected $utils;

    /**
     * @var FileManager
     */
    protected $fileManager;

    /**
     * Return agencies present in specific cms table (not agenzie, just to be clear).
     *
     * @routing(method="GET", pattern="/")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function indexAction(Request $Req, Response $Res)
    {

        $data = $Req->getGetData();
        $paginationData = $this->makePaginationData($data);

        $data = $this->agenciesStatus->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            $paginationData->criteriaExp
        );

        $result['items'] = Json::jsonCollection($data);
        $result['total'] = $this->agenciesStatus->count($paginationData->criteriaExp);

        return $Res->set('data', $result)->setView('json:');
    }

    /**
     * Return agencies present in specific cms table (not agenzie, just to be clear).
     *
     * @routing(method="GET", pattern="/agencies")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function indexNextAction(Request $Req, Response $Res)
    {

        $data = $Req->getGetData();
        $paginationData = $this->makePaginationData($data);
        /*print_r($data);
        return;*/

        $data = $this->agenciesStatus->fetchAll(
            $data['page'],
            $data['pageSize'],
            $data['orderExp'],
            $data['criteriaExp']
        );

        $result['data'] = Json::jsonCollection($data);
        $result['total'] = $this->agenciesStatus->count($data['criteriaExp']);
        return $Res->set('data', $result)->set('success', true)->setView('json:');

        return $Res->set('data', $result)->setView('json:');
    }

    /**
     * Return agency data.
     *
     * @routing(method="GET", pattern="/agency-detail")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function agencyDataAction(Request $Req, Response $Res)
    {

        if (!$agenzia_id = $Req->get('agenzia_id')) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        $cmsData = $this->agenciesData->fetchOne(null, null, "agenzia_id,EQ,$agenzia_id|approved,EQ,0", Repository::FETCH_ARRAY);
        $agencyData = $this->agencies->fetch($agenzia_id, Repository::FETCH_ARRAY);
        $agencySchedule = $this->schedules->getAgencySchedule($agenzia_id, $cmsData);

        $response = $this->formatDetailData($agencyData, $cmsData, $agencySchedule);

        return $Res->set('data', $response)->setView('json:');
    }

    protected function formatDetailData($agencyData, $cmsData, $agencySchedule)
    {
        return array(
            "info" => [
                "agenzia_id" => $agencyData['id'],
                "groupama_id" => $cmsData['groupama_id'],
                "localita" => $agencyData['localita'],
                "nome" => $agencyData['nome'],
                "indirizzo" => $agencyData['indirizzo'],
                "cap" => $agencyData['cap'],
                "citta" => $agencyData['citta'],
                "provincia" => $agencyData['provincia'],
                "regione" => $agencyData['regione'],
                "telefono" => $agencyData['telefono'],
                "fax" => $agencyData['fax'],
                "email" => $agencyData['email'],
                "link" => $cmsData['link'],
                "shortLink" => $cmsData['shortLink'],
            ],
            "cms" => [
                "agencyEntrancePhoto" => $cmsData['agencyEntrancePhoto'],
                "url" => $cmsData['url'],
                "whatsapp" => $cmsData['whatsapp'],
                "description" => $cmsData['description'],
                "approved" => $cmsData['approved'],
                "pec" => $cmsData['pec'],
                "showWeeklySchedule" => $cmsData['showWeeklySchedule'],
            ],
            "schedule" => $agencySchedule
        );
    }

    /**
     * Return user legacy data.
     *
     * @routing(method="GET", pattern="/user-detail")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function userAction(Request $Req, Response $Res)
    {

        if (!$user_id = $Req->get('user_id')) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        $user = $this->employees->fetchOne(null, null, "user_id,EQ,$user_id|approved,EQ,0", Repository::FETCH_ARRAY);

        return $Res->set('data', $user)->setView('json:');
    }

    /**
     * Return agency employees.
     *
     * @routing(method="GET", pattern="/employees")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function employeesAction(Request $Req, Response $Res)
    {
        if (!$agenzia_id = $Req->get('agenzia_id')) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        $data = $Req->getGetData();
        $paginationData = $this->makePaginationData($data);
        $paginationData->criteriaExp ? $paginationData->criteriaExp .= "|agenzia_id,EQ,$agenzia_id" : $paginationData->criteriaExp = "agenzia_id,EQ,$agenzia_id";

        if ($_SESSION['AUTH']['UTYPE'] === 'AGENTE') {
            $paginationData->criteriaExp .= "|type,EQ,INTERMEDIARIO";
        }

        $paginationData->criteriaExp .= "|approved,EQ,0";

        $result['items'] = $this->employees->fetchAll($paginationData->page, $paginationData->pageSize, $paginationData->orderExp, $paginationData->criteriaExp, Repository::FETCH_ARRAY);
        $result['total'] = $this->employees->count($paginationData->criteriaExp);

        return $Res->set('data', $result)->setView('json:');
    }

    protected function findCmsMatch($id)
    {
        // Return false if no match
        if (!$match = $this->employees->fetchAll(null, null, null, "user_id,EQ,$id", Repository::FETCH_ARRAY)) {
            return false;
        }

        return $match[0];
    }

    protected function formatEmployeeData($userData, $cmsData = null)
    {
        return array(
            "user_id" => $userData['id'],
            "nome" => $userData['nome'],
            "cognome" => $userData['cognome'],
            "agenzia_id" => $userData['agenzia_id'],
            "type" => $userData['type'],
            "role" => $userData['ruolo'],
            "rui" => $userData['rui'],
            "showOnFrontend" => $cmsData['showOnFrontend'],
            "privacy" => $cmsData['privacy'],
            "position" => $cmsData['position'],
        );
    }

    /**
     * Show/hide agency from site frontend.
     *
     * @routing(method="GET", pattern="/status-change")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function statusChangeAction(Request $Req, Response $Res)
    {

        if (!$agenzia_id = $Req->get('agenzia_id')) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        $status = $Req->get('status');

        try {

            if ( $status ) {
                // Recupero i dati non approvati
                $unapprovedData = $this->agenciesData->fetch(["agenzia_id" => $agenzia_id, "approved" => '0'], Repository::FETCH_ARRAY);
                $unapprovedUsers = $this->employees->fetchAll(null, null, null, "agenzia_id,EQ,$agenzia_id|approved,EQ,0", Repository::FETCH_ARRAY);

                $this->fileManager->removeOldApprovedPhotos($agenzia_id, $unapprovedData);

                // Sovrascrivo approvati con i non approvati appena recuperati
                $this->agenciesData->alignAgencyData($unapprovedData, $agenzia_id);
                $this->employees->alignUserData($unapprovedUsers);
            }

            $this->agenciesStatus->changeAgencyStatus($agenzia_id, (int)$status);
        } catch (\Exception $ex) {
            $Res
                ->set('success', false)
                ->set('data', "Errore imprevisto")
                ->setView("json:");
        }

        return $Res->set('success', true)->setView('json:');
    }

    /**
     * Update cms agency data
     *
     * @routing(method="POST", pattern="/update-cms/data/<agenzia_id>")
     * @param Request $Req
     * @param Response $Res
     * @param String $agenzia_id
     *
     * @return json
     */
    public function updateAgencyCmsDataAction(Request $Req, Response $Res, $agenzia_id)
    {

        if (!$data = json_decode($Req->getRawData(), true)) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        //$this->agenciesData->updateData($data, $agenzia_id,true);
        $changes = $this->checkForChanges($agenzia_id, $data);

        if ($_SESSION["AUTH"]["UTYPE"] === 'AGENTE') {

            $changes = $this->checkForChanges($agenzia_id, $data);

            try {

                $this->agenciesStatus->update($agenzia_id, ["approved" => 0, "standard" => 0, "user_id" => $_SESSION["AUTH"]["UID"]]);
                $result = $this->agenciesData->updateData($data, $agenzia_id);

                if ($changes) {
                    $this->notifyAdminsAgencyDetailChange($agenzia_id, $changes);
                }
            } catch (\Exception $ex) {
                $Res
                    ->set('success', false)
                    ->set('data', "Errore nell'aggiornamento dei dati")
                    ->setView("json:");
            }

        }
        else {
            try {

                $result = $this->agenciesData->updateData($data, $agenzia_id,true);

            } catch (\Exception $ex) {
                $Res
                    ->set('success', false)
                    ->set('data', "Errore nell'aggiornamento dei dati")
                    ->setView("json:");
            }

        }

        return $Res->set('success', true)->set('data', $result)->setView('json:');

    }

    /**
     * Update cms agency schedule
     *
     * @routing(method="POST", pattern="/update-cms/schedule/<agenzia_id>")
     * @param Request $Req
     * @param Response $Res
     * @param String $agenzia_id
     *
     * @return json
     */
    public function updateAgencyCmsScheduleAction(Request $Req, Response $Res, $agenzia_id)
    {

        if (!$data = json_decode($Req->getRawData(), true)) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        if (!$days = $this->schedules->saveAgencySchedule($data['schedule']['days'])) {
            http_response_code(500);
            return $Res->set('success', "false")->setView('json:');
        }

        $changes = $this->checkForChanges($agenzia_id, $data, true);
        /*$this->debugThing($data);
        return;*/

        if ($_SESSION["AUTH"]["UTYPE"] === 'AGENTE') {

            try {

                $this->agenciesStatus->update($agenzia_id, ["approved" => 0, "standard" => 0, "user_id" => $_SESSION["AUTH"]["UID"]]);
                $this->agenciesData->updateSchedule($data, $agenzia_id);

                if ($changes) {
                    $this->notifyAdminsAgencyDetailChange($agenzia_id, $changes);
                }

            } catch (\Exception $ex) {
                $Res
                    ->set('success', false)
                    ->set('data', "Errore nell'aggiornamento dei dati")
                    ->setView("json:");
            }

        }
        else {
            try {

                $this->agenciesData->updateSchedule($data, $agenzia_id,true);

            } catch (\Exception $ex) {
                $Res
                    ->set('success', false)
                    ->set('data', "Errore nell'aggiornamento dei dati")
                    ->setView("json:");
            }

        }

        return $Res->set('success', true)->setView('json:');
    }

    /**
     * Update cms employee details
     *
     * @routing(method="POST", pattern="/update-cms/details/<agenzia_id>")
     * @param Request $Req
     * @param Response $Res
     * @param String $agenzia_id
     *
     * @return json
     */
    public function updateEmployeeCmsDataAction(Request $Req, Response $Res, $agenzia_id)
    {
        if (!$data = json_decode($Req->getRawData(), true)) {
            http_response_code(400);
            return $Res
                ->set('success', "false")
                ->set('data', "data not found")
                ->setView('json:');
        }

        if (!$agenzia_id) {
            $this->trace(LOG_ERR, 1, __FUNCTION__, "agenzia_id not found");
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }
        if (!$user_id = $data['user_id']) {
            $this->trace(LOG_ERR, 1, __FUNCTION__, "id not found");
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        $legacyData = [
            "type" => $data['type'],
            "nome" => $data['nome'],
            "cognome" => $data['cognome'],
            "rui" => $data['rui'],
            "email" => $data['email'],
            "ruolo" => $data['ruolo']
        ];

        $cmsData = [
            "position" => $data['position'],
            "description" => $data['description']
        ];

        $descriptionChange = false;

        $oldData = $this->employees->fetchOne(null, null, "user_id,EQ,$user_id|approved,EQ,0", Repository::FETCH_ARRAY);
        if ($oldData['description'] != $data['description']) {
            $descriptionChange = true;
        }

        try {
            $this->pdoBeginTransaction();

            // Aggiorno i dati dell'utente
            $this->users->update($user_id, $legacyData, true, Repository::FETCH_ARRAY);
            $this->employees->updateData($cmsData, $user_id);

            // Se utente loggato è agente ed è cambiata la descrizione dell'utente, setto l'agenzia a non approvata e notifico admin
            if ($_SESSION["AUTH"]["UTYPE"] === 'AGENTE' && $descriptionChange) {
                $this->agenciesStatus->changeAgencyStatus($agenzia_id, 0);
                $this->notifyAdminsUserDetailChange($agenzia_id, $data);
            }

            $this->pdoCommit();
        } catch (\Exception $ex) {
            $this->pdoRollback();
            $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
            http_response_code(500);
            return $Res
                ->set('success', false)
                ->set('data', $ex->getMessage())
                ->setView("json:");
        }

        return $Res->set('success', true)->setView('json:');
    }

    /**
     * Check if Agent has accepted privacy policy
     *
     * @routing(method="GET", pattern="/privacy/status")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function checkAgentPrivacyAction(Request $Req, Response $Res)
    {

        if (!$agent = $this->employees->checkPrivacy($_SESSION["AUTH"]["UID"])) {
            http_response_code(401);
            return $Res->set('success', false)->setView('json:');
        }

        return $Res->set('success', true)->set('privacy', $agent['privacy'])->setView('json:');
    }

    /**
     * Change privacy status
     *
     * @routing(method="POST", pattern="/privacy/change")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function privacyChangeAction(Request $Req, Response $Res)
    {

        if (!$data = json_decode($Req->getRawData(), true)) {
            http_response_code(400);
            return $Res
                ->set('success', false)
                ->set('data', "no data")
                ->setView('json:');
        }

        try {
            $this->employees->setPrivacy($_SESSION["AUTH"]["UID"], $data);
        } catch (\Exception $ex) {
            $Res
                ->set('success', false)
                ->set('data', $ex->getMessage())
                ->setView("json:");
        }
        $this->trace(LOG_DEBUG, 1, __FUNCTION__, "DATA", print_r($data,1));

        if ($data['gdpr'] && $data['consent']) {

            $user = $this->users->fetch($_SESSION["AUTH"]["UID"]);
            $addresses = [$user->email => $user->nome . ' ' . $user->cognome];

            if (\metadigit\core\ENVIRONMENT !== 'PROD') {
                $addresses = ["<EMAIL>", "<EMAIL>"];
            }

            $body = "
                <p>
                <strong>Benvenuto in MyPage!</strong>
                <br>
                a breve il tuo profilo comparirà nella pagina agenzia su groupama.it.
                </p>
                <p>
                Le tue impostazioni privacy sono state registrate sul Portale Agendo.
                <br>
                In allegato puoi trovare copia PDF dell'informativa della liberatoria da te accettate, per dubbi o approfondimenti puoi contattare il tuo District Manager di riferimento.
                </p>
                <p>
                Grazie, 
                <br>
                il team MyPage
                </p>
                <br>";

            $this->utils->sendEmail($addresses, $body, "Benvenuto in MyPage, abbiamo registrato le tue preferenze privacy", true);
        }

        return $Res->set('success', true)->setView('json:');
    }

    /**
     * Send email with privacy policy acceptance request
     *
     * @routing(method="POST", pattern="/update-cms/privacy/<user_id>")
     * @param Request $Req
     * @param Response $Res
     * @param Int user_id
     *
     * @return json
     */
    public function notifyEmpolyeeAction(Request $Req, Response $Res, $user_id)
    {
        if (!$user_id) {
            $this->trace(LOG_ERR, 1, __FUNCTION__, "user_id not found");
            http_response_code(400);
            return $Res
                ->set('success', false)
                ->set('data', "Missing user_id")
                ->setView('json:');
        }

        try {
            $latestSentTimestamp = false;
            $employeeMails = $this->employeeMails->getByUserId($user_id);
            if ($employeeMails && count($employeeMails)) {
                $latestSentTimestamp = strtotime($employeeMails[0]['created_at']);
            }

            if ($latestSentTimestamp && $latestSentTimestamp > strtotime(self::MAIL_DELAY_TIME)) {
                $this->trace(LOG_ERR, 1, __FUNCTION__, "Too many emails sent");
                return $Res
                    ->set('message', "Too many emails sent")
                    ->set('errorCode', 498)
                    ->set('success', false)->setView('json:');
            }

            $user = $this->users->fetch($user_id);
            $this->trace(LOG_INFO, 1, __FUNCTION__, "user: " . $user->nome . " " . $user->cognome . " - " . $user->email);
            if (!$user) {
                $this->trace(LOG_ERR, 1, __FUNCTION__, "User not found: " . $user_id);
                http_response_code(400);
                return $Res
                    ->set('data', "User not found")
                    ->set('success', false)->setView('json:');
            }
            if (!$user->email) {
                $this->trace(LOG_ERR, 1, __FUNCTION__, "User email empty" . $user_id . " - " . $user->nome . " " . $user->cognome);
                return $Res
                    ->set('message', "User email empty")
                    ->set('errorCode', 499)
                    ->set('success', false)->setView('json:');
            }

            $toEmail = [$user->email => $user->nome . " " . $user->cognome];
            if (\metadigit\core\ENVIRONMENT !== 'PROD') {
                $toEmail = ["<EMAIL>", "<EMAIL>"];
            }

            if (! $this->utils->sendEmail($toEmail, file_get_contents(self::MAIL_BODY), self::MAIL_SUBJECT)) {
                $this->trace(LOG_ERR, 1, __FUNCTION__, "Error sending email");
                return $Res
                    ->set('data', "Error sending email")
                    ->set('errorCode', 497)
                    ->set('success', false)->setView('json:');
            };

            $this->employeeMails->insert(null, [
                'user_id' => $user_id,
                'email' => $user->email,
                'created_at' => date("Y-m-d H:i:s"),
                'sender' => $_SESSION["AUTH"]["UID"],
            ]);

            $this->employees->update($user_id, ["privacy" => "WAIT"]);
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
            http_response_code(500);
            $Res
                ->set('success', false)
                ->set('data', "Error sending email")
                ->setView("json:");
        }

        $this->trace(LOG_INFO, 1, __FUNCTION__, "Email sent to " . $user->email);
        $Res
            ->set('success', true)
            ->set('data', "Email sent to " . $user->email)
            ->setView("json:");
    }

    /**
     * Check privacy request
     *
     * @routing(method="GET", pattern="/privacy-request/status")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function checkPrivacyRequestAction(Request $Req, Response $Res)
    {

        if (!$intermediario = $this->employees->checkPrivacy($_SESSION["AUTH"]["UID"])) {
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        $isWait = $intermediario['privacy'] === 'WAIT';

        return $Res->set('success', true)->set('request', $isWait)->setView('json:');
    }

    /**
     * Check privacy request
     *
     * @routing(method="GET", pattern="/visibility/<user_id>/<status>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $user_id
     * @param boolean $status
     *
     * @return json
     */
    public function changeVisibilityStatusAction(Request $Req, Response $Res, int $user_id, bool $status)
    {
        $status = $this->employees->update($user_id, ["showOnFrontend" => $status]);

        return $Res->set('success', true)->set('data', $status)->setView('json:');
    }

    /**
     * Send email with edit request
     *
     * @routing(method="POST", pattern="/edit-request/<agenzia_id>")
     * @param Request $Req
     * @param Response $Res
     * @param Int $agenzia_id
     *
     * @return json
     */
    public function sendEditRequestAction(Request $Req, Response $Res, $agenzia_id)
    {

        $message = $Req->getRawData();

        $agency = $this->agencies->fetch($agenzia_id);
        $employees = $this->employees->fetchAll(null, null, null, "agenzia_id,EQ,$agenzia_id|type,EQ,AGENTE", Repository::FETCH_ARRAY);

        $addresses = [];
        foreach ($employees as $employee) {
            if ($employee['email']) {
                $addresses[] = $employee['email'];
            }
        }
        $addresses[] = $agency->email;
        $bccAddresses = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

        if (\metadigit\core\ENVIRONMENT !== 'PROD') {
            $addresses = ["<EMAIL>", "<EMAIL>"];
            $bccAddresses = null;
        }

        $body = "<h1>Richiesta di modifica su MyPage</h1>
            <p>Gentile Agente,<br>il team di Compagnia ha analizzato i dati che hai inserito nella scheda della tua agenzia su MyPage e ha formulato la seguente richiesta:</p>
            <div style='padding: 20px; border: 1px solid grey; margin-bottom: 20px; border-radius: 5px; font-style: italic'>$message</div>
            <p>Accedi a <a href='https://www.portaleagendo.it'>Portale Agendo</a> ed entra in MyPage per apportare le modifiche.</p>
            <br>
            <br>
            <p>
                Grazie
                <br>
                il team di MyPage
            </p>";

        if ( ! $this->utils->sendEmail($addresses, $body, "Richiesta di modifica su MyPage", false, $bccAddresses) ) {
            $this->trace(LOG_ERR, 1, __FUNCTION__, "Error sending email");
            return $Res
                ->set('data', "Error sending email")
                ->set('errorCode', 497)
                ->set('success', false)->setView('json:');
        };

        // Disabilito validazione degli indirizzi (vedi mail del 23/5/20223 di martella "indirizzi mail")
        //$addresses = $this->validateAddresses($addresses);

        return $Res
            ->set('data', $addresses)
            ->set('success', true)->setView('json:');

    }


    /**
     * @param array $addresses
     * @param string $body
     * @param string $subject
     * @return bool|integer
     */
    protected function sendEmail(array $addresses, string $body, string $subject, $attachment = false, array $bccAddresses = null, $skipValidation = false)
    {
        $mail = $this->mailer->newMessage();

        //$this->trace(LOG_DEBUG, 1, __FUNCTION__, "Collected addresses", print_r($addresses, 1));
        if (! $skipValidation) {
            // Disabilito validazione degli indirizzi (vedi mail del 23/5/20223 di martella "indirizzi mail")
            //$addresses = $this->validateAddresses($addresses);
        }
        //$this->trace(LOG_DEBUG, 1, __FUNCTION__, "Cleaned addresses", print_r($addresses, 1));

        if (\metadigit\core\ENVIRONMENT !== 'PROD') {
            $addresses = ["<EMAIL>"];
            $bccAddresses = ["<EMAIL>"];
        }

        //$this->trace(LOG_DEBUG, 1, __FUNCTION__, "CCN addresses", print_r($bccAddresses, 1));

        $mail->setSubject($subject)
            ->setTo($addresses)
            ->setBcc($bccAddresses)
            ->setFrom(['<EMAIL>' => 'ROBOT PortaleAgendo'])
            ->setReplyTo('<EMAIL>')
            ->setReturnPath('<EMAIL>')
            ->setContentType('text/html')
            ->setBody($body);

        if ($attachment) {
            $mail->attach(\Swift_Attachment::fromPath(ASSETS_DIR . 'privacy-policy.pdf', 'application/pdf')->setFilename('privacy-policy.pdf'));
        }

        if ( ! $successfulDeliveries = $this->mailer->send($mail) ) {
            return false;
        };

        return $successfulDeliveries;

    }


    /**
     * @param $agenzia_id
     * @return void
     * @throws \metadigit\core\db\orm\Exception
     */
    protected function notifyAdminsAgencyDetailChange($agenzia_id, $data = null, $isSchedule = false)
    {

        $changedFieldsHtml = "";
        // Per ogni campo differente formatto la label e lo aggiungo come li dentro ul
        foreach ($data as $value) {
            $changedFieldsHtml .= "<li>". $this->utils->formatFieldName($value) ."</li>";
        }

        $body = "<h1>Richiesta approvazione dati Agenzia $agenzia_id</h1>
                <p>Alcune informazioni dell'Agenzia $agenzia_id sono state modificate dall'agente.</p>
                <p>Nella pagina agenzia del Sito Istituzionale saranno visualzzate informazioni parziali finché le modifiche non verranno approvate.</p>
                <p>Accedi a MyPage nel <a href='https://www.portaleagendo.it'>Portale Agendo</a> per approvare tali modifiche.</p>
                <br>
                <div style='padding: 20px; border: 1px solid grey; margin-bottom: 20px; border-radius: 5px; font-style: italic'>
                    <h3>Campi modificati:</h3>
                    <ul>
                        $changedFieldsHtml
                    </ul>
                </div>
                <br>
                <p>Grazie</p>";

        $addresses = [
            '<EMAIL>' => 'Francesco Giudici',
            '<EMAIL>' => 'Gabriele Di Napoli',
        ];

        if (\metadigit\core\ENVIRONMENT !== 'PROD') {
            $addresses = ["<EMAIL>", "<EMAIL>"];
        }

        $this->utils->sendEmail($addresses, $body, "Richiesta approvazione dati Agenzia $agenzia_id", null, ["<EMAIL>"], true);

    }

    /**
     * @param $agenzia_id
     * @return void
     * @throws \metadigit\core\db\orm\Exception
     */
    protected function notifyAdminsUserDetailChange($agenzia_id, $data = null)
    {

        $body = "<h1>Richiesta approvazione dati Agenzia $agenzia_id</h1>
                <p>La descrizione di {$data['nome']} {$data['cognome']} dell'Agenzia $agenzia_id è stata modificata</p>
                <p>Nella pagina agenzia del Sito Istituzionale saranno visualzzate informazioni parziali finché le modifiche non verranno approvate.</p>
                <p>Accedi a MyPage nel <a href='https://www.portaleagendo.it'>Portale Agendo</a> per approvare tali modifiche.</p>
                <br>
                <br>
                <p>Grazie</p>";

        $addresses = [
            '<EMAIL>' => 'Francesco Giudici',
            '<EMAIL>' => 'Gabriele Di Napoli',
        ];

        if (\metadigit\core\ENVIRONMENT !== 'PROD') {
            $addresses = ["<EMAIL>", "<EMAIL>"];
        }

        $this->utils->sendEmail($addresses, $body, "La descrizione di {$data['nome']} {$data['cognome']} dell'Agenzia $agenzia_id è stata modificata", null, ["<EMAIL>"], true);

    }

    /**
     * @param $addresses
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    protected function validateAddresses($addresses)
    {

        $validAddresses = [];

        foreach ($addresses as $address) {
            if ( strstr($address, '@groupama.it') !== false || strstr($address, '@nuovatirrena.net') !== false ) {
                $validAddresses[] = $address;
            }
        }

        return $validAddresses;

    }

    /**
     * @param $agenzia_id
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    protected function checkForChanges($agenzia_id, $data = null, $isSchedule = false)
    {

        // Prendo dati salvati per confrontarli con quelli provenienti dal form
        $oldData = $this->agenciesData->fetchOne(null, null, "agenzia_id,EQ,$agenzia_id|approved,EQ,0", Repository::FETCH_ARRAY);

        if ($isSchedule) {
            $oldData['text'] !== $data['schedule']['text'] ? $changedFields = [ 'text' ] : $changedFields = [];
        }
        else {
            // Confronto i dati salvati (oldData) con i dati provenienti dal form (data)
            $changedFields = $this->getArrayDifferences($data, $oldData);
        }

        return $changedFields;

    }

    /**
     * @param array $arr1
     * @param array $arr2
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    protected function getArrayDifferences(array $arr1, array $arr2)
    {
        $differences = [];
        foreach ($arr1 as $key => $value) {
            if ($arr2[$key] !== $value) {
                $differences[] = $key;
            }
        }

        return $differences;

    }

    /**
     * Return agency data.
     *
     * @routing(method="GET", pattern="/agency-status")
     * @param Request $Req
     * @param Response $Res
     * @param string $agenzia_id
     *
     * @return json
     */
    public function agencyStatusAction(Request $Req, Response $Res, string $agenzia_id)
    {

        if (!$agenzia_id) {
            http_response_code(400);
            return $Res->set('success', "false")->setView('json:');
        }

        $agency = $this->agenciesStatus->fetch($agenzia_id, Repository::FETCH_ARRAY);

        return $Res->set('data', $agency['approved'])->setView('json:');
    }

    /**
     * Download excel of agencies table
     *
     * @routing(method="GET", pattern="/excel")
     * @param Request $Req
     * @param Response $Res
     */
    public function downloadExcelAction(Request $Req, Response $Res)
    {

        $data = $Req->getGetData();
        $paginationData = $this->makePaginationData($data);

        $data = $this->agenciesStatus->fetchAll(
            null,
            null,
            $paginationData->orderExp,
            $paginationData->criteriaExp
        );

        $timeStamp = date('d-m-Y h:i:s', time());

        return $Res
            ->set("filename", "MyPage - Elenco Agenzie $timeStamp.xls")
            ->set("agencies", $data)
            ->setView("php:/mypage-agencies");
    }

    /**
     * Check if logged agency is allowed to MyPage
     *
     * @routing(method="GET", pattern="/access")
     * @param Request $Req
     * @param Response $Res
     * @param string $agenzia_id
     *
     * @return json
     */
    public function checkAccessAction(Request $Req, Response $Res)
    {

        if (! $agenziaId = $_SESSION['AUTH']['AGENZIA']) {
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        if ( $this->agenciesStatus->fetch($agenziaId, Repository::FETCH_ARRAY) ) {
            return $Res->set('success', true)->set('access', true)->setView('json:');
        }

        return $Res->set('success', true)->set('access', false)->setView('json:');

    }
}
