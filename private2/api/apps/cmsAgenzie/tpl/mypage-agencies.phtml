<?php
header("Content-Type: application/octet-stream");
header("Content-Transfer-Encoding: Binary");
header("Content-disposition: attachment; filename=\"{$filename}\"");

function _agencyStatusFormat($approved, $standard) {
    $string = '';
    if ( !(int)$standard && !(int)$approved ) {
        $string = 'Da approvare';
    }
    else if ( (int)$standard && (int)$approved ) {
        $string = 'Standard';
    }
    else if ( !(int)$standard && (int)$approved ) {
        $string = 'Approvata';
    }
    return $string;
}

function _agencyPhotoFormat($value) {
    $string = '';
    switch ($value) {
        case 'SI':
            $string = "Inserita";
            break;
        case 'NO':
            $string = "Mancante";
            break;
    }
    return $string;
}

function _agencyApprFormat($value) {
    $string = '';
    switch ($value) {
        case 'FULL':
            $string = "Completo";
            break;
        case 'PARTIAL':
            $string = "Parziale";
            break;
        case 'EMPTY':
            $string = "Vuoto";
            break;
    }
    return $string;
}

?>
<table>
    <thead>
    <tr>
        <th style="border-bottom: 1px solid #000000">Cod. GA</th>
        <th style="border-bottom: 1px solid #000000">Cod. AGE</th>
        <th style="border-bottom: 1px solid #000000">Nome Agenzia</th>
        <th style="border-bottom: 1px solid #000000">R. Sociale</th>
        <th style="border-bottom: 1px solid #000000">Localita</th>
        <th style="border-bottom: 1px solid #000000">Agente</th>
        <th style="border-bottom: 1px solid #000000">Status</th>
        <th style="border-bottom: 1px solid #000000">Immagine Age.</th>
        <th style="border-bottom: 1px solid #000000">Approfondimento</th>
        <th style="border-bottom: 1px solid #000000">Team</th>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($agencies as $a):?>
    <tr>
        <td style="vertical-align: top"><?php echo $a->groupama_id ?></td>
        <td style="vertical-align: top"><?php echo $a->agenzia_id ?></td>
        <td style="vertical-align: top"><?php echo $a->nome ?></td>
        <td style="vertical-align: top"><?php echo $a->ragioneSociale ?></td>
        <td style="vertical-align: top"><?php echo $a->localita ?></td>
        <td style="vertical-align: top"><?php echo $a->agents ?></td>
        <td style="vertical-align: top"><?php echo _agencyStatusFormat($a->approved, $a->standard) ?></td>
        <td style="vertical-align: top"><?php echo _agencyPhotoFormat($a->hasAgencyPhoto) ?></td>
        <td style="vertical-align: top"><?php echo _agencyApprFormat($a->detail) ?></td>
        <td style="vertical-align: top"><?php echo $a->privacyAccepted.'|'.$a->teamTotal ?></td>
    </tr>
    <?php endforeach;?>
    </tbody>
</table>
